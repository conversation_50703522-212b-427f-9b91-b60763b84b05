# GEMINI.md

## Project Overview

This project is a **Prompt Optimizer**, a powerful AI tool designed to help users write better AI prompts and improve the quality of AI-generated content. It is a monorepo built with pnpm, containing several packages that work together to provide a seamless user experience across multiple platforms.

The application is available in four different formats:

- **Web Application:** A web-based interface for optimizing prompts.
- **Desktop Application:** A native desktop application for Windows, macOS, and Linux.
- **Chrome Extension:** A browser extension for optimizing prompts directly in the browser.
- **Docker Image:** A Docker image for easy deployment.

### Main Technologies

- **Frontend:** Vue.js, Element Plus
- **Backend:** Node.js (for the desktop app and MCP server)
- **Build Tool:** Vite
- **Package Manager:** pnpm
- **Language:** TypeScript

### Architecture

The project follows a monorepo architecture, with the following packages:

- `packages/core`: Contains the core business logic of the application, including services for managing templates, history, LLMs, models, storage, prompts, and user preferences.
- `packages/ui`: Contains the Vue.js components for the user interface, built with Element Plus.
- `packages/web`: The web application.
- `packages/desktop`: The Electron-based desktop application.
- `packages/extension`: The Chrome extension.
- `packages/mcp-server`: A server that implements the Model Context Protocol (MCP) for integration with other AI applications.

## Building and Running

### Prerequisites

- Node.js >= 18
- pnpm >= 8
- Git >= 2.0

### Installation

```bash
pnpm install
```

### Development

**Web Application:**

```bash
# Start the development server for the web application
pnpm dev

# Clean, reinstall dependencies, and start the development server
pnpm dev:fresh
```

**Desktop Application:**

```bash
# Start the development server for the desktop application
pnpm dev:desktop

# Clean, reinstall dependencies, and start the development server
pnpm dev:desktop:fresh
```

### Building

```bash
# Build all packages
pnpm build

# Build a specific package
pnpm build:core
pnpm build:ui
pnpm build:web
pnpm build:ext
pnpm build:desktop
```

### Testing

```bash
# Run tests for all packages
pnpm test

# Run tests for a specific package
pnpm -F @prompt-optimizer/core test
```

## Development Conventions

### Commit Messages

The project uses the [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages. The format is as follows:

```
<type>(<scope>): <subject>
```

**Example:**

```
feat(ui): add new prompt editor component
fix(core): resolve API timeout issue
```

### Branching Model

The project uses a GitFlow-like branching model:

- `main`: The production branch.
- `develop`: The development branch.
- `feature/*`: Feature branches.

### Versioning

The project uses semantic versioning. The version number is managed using the `pnpm version` command.
