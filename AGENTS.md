# Repository Guidelines

## Project Structure & Module Organization

- Monorepo managed by `pnpm` (see `pnpm-workspace.yaml`).
- Packages:
  - `packages/core`: TypeScript core library.
  - `packages/ui`: Reusable Vue 3 UI components.
  - `packages/web`: Vite app for the web UI.
  - `packages/extension`: Browser extension (Vite + Vue).
  - `packages/desktop`: Electron desktop app (bundles `web`).
  - `packages/mcp-server`: MCP server (Node/TS).
- Other:
  - `api/`: Vercel serverless functions.
  - `node-proxy/`: Lightweight proxy service.
  - `docs/`: Architecture, user guides, testing notes.
  - `scripts/`: Repo maintenance scripts (e.g., `sync-versions.js`).

## Build, Test, and Development Commands

- Install: `pnpm install`
- Build all: `pnpm build` (core → ui → web/extension in parallel)
- Dev web: `pnpm dev` (builds core/ui, runs web with watch)
- Dev desktop: `pnpm dev:desktop`
- Dev extension: `pnpm dev:ext`
- Tests (all packages): `pnpm test`
- Package desktop only: `pnpm -F @prompt-optimizer/desktop build`
- MCP server: `pnpm mcp:build | mcp:dev | mcp:start | mcp:test`

## Coding Style & Naming Conventions

- Languages: TypeScript + Vue 3, Node/Electron.
- Indentation: 2 spaces; use ES modules.
- Vue components: PascalCase filenames (e.g., `OutputPanel.vue`).
- Packages and paths: kebab-case; scoped names `@prompt-optimizer/*`.
- Linting: ESLint in `packages/mcp-server`; follow Vue/TS best practices elsewhere.

## Testing Guidelines

- Framework: Vitest across packages (`vitest.config.*`).
- Run unit tests: `pnpm -F <pkg> test` (e.g., `-F @prompt-optimizer/web`).
- Coverage: `pnpm -F <pkg> test:coverage` where available.
- Test files: `*.test.ts` under `tests/` or co-located with sources.

## Commit & Pull Request Guidelines

- Conventional style used in history (e.g., `feat(core): add X`, `fix(web): ...`, `docs(README): ...`).
- Commits should be scoped (`core`, `ui`, `web`, `extension`, `desktop`, `mcp-server`, `proxy`, `docs`).
- PRs: include clear description, linked issues, repro steps, and relevant screenshots/logs.
- Version sync: run `pnpm run version:sync` when changing versions.

## Security & Configuration Tips

- Environment: copy `env.local.example` → `.env`/`.env.local` as needed.
- Node: use `^18/20/22` (see `package.json` engines).
- Docker/Vercel: see `docker-compose*.yml`, `Dockerfile`, and `docs/user/deployment/`.
