# Prompt Optimizer 桌面用户手册

## 1. 欢迎使用

欢迎使用 Prompt Optimizer 桌面版！本应用旨在提供一个流畅、无障碍的提示词优化体验，让您可以专注于创造高质量的提示词，而无需担心网络配置问题。

与 Web 版本最大的不同是，桌面版**无需任何代理或复杂的设置，即可直接调用各大 AI 服务商的 API**。

## 2. 安装与启动

### 系统要求

- Windows 10/11, macOS, 或 Linux
- 稳定的网络连接（用于 API 调用）

### 安装步骤

1.  从指定的发布页面下载适合您操作系统的安装包（例如，Windows 用户下载 `.exe` 文件）。
2.  双击安装包，按照屏幕上的提示完成安装。
3.  安装完成后，在您的桌面或应用程序列表中找到 "Prompt Optimizer" 图标并启动它。

## 3. 首次配置：连接您的 AI 服务

为了让应用能够工作，您需要提供至少一个 AI 服务商的 API 密钥。

1.  启动应用后，点击侧边栏的 **"模型管理"** 图标。
2.  在模型管理页面，您会看到一个模型列表。选择您想使用的服务商（如 OpenAI, DeepSeek 等）。
3.  在对应的输入框中，**填入您的 API Key**。
4.  点击 **"测试连接"** 按钮。如果密钥有效且网络正常，您会看到 "连接成功" 的提示。
5.  确保您想使用的模型旁边的 **"启用"** 开关是打开状态。

## 4. 基本使用方法

1.  **输入内容**: 在左侧的 "待优化内容" 输入框中，输入您想要优化的原始提示词或文本。
2.  **选择模板**: 在上方的 "优化模板" 下拉菜单中，选择一个优化策略。对于大多数情况，"通用优化" 是一个不错的开始。
3.  **点击优化**: 点击中间的 **"优化"** 按钮。
4.  **查看结果**: 右侧的 "优化结果" 区域将显示由 AI 生成的优化后的提示词。

## 5. 常见问题 (FAQ)

**Q1: 点击"优化"后没有任何反应或提示错误，该怎么办？**

**A:** 请按以下步骤排查：

1.  确保您的电脑已连接到互联网。
2.  前往"模型管理"页面，再次点击"测试连接"，确认您的 API 密钥仍然有效。
3.  确认您选择的优化模板所对应的模型已经启用并且配置正确。

**Q2: 应用界面显示为空白？**

**A:** 这可能是应用资源加载失败。请尝试完全关闭应用后重新启动。如果问题仍然存在，请考虑重新安装应用。

**Q3: 我可以同时使用多个 AI 服务商吗？**

**A:** 可以。您可以在"模型管理"中配置并启用多个服务商和模型。在使用时，可以在"优化模板"或相关设置中选择具体使用哪个模型进行优化。

**Q4: 我的 API 密钥安全吗？**

**A:** 是的。在桌面版中，您的 API 密钥仅存储在您本地计算机上，并且只在您发起优化请求时直接发送给对应的 AI 服务商，不会经过任何第三方服务器。
