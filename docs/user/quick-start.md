# 快速上手指南

## 🚀 欢迎使用提示词优化器

提示词优化器是一个智能工具，帮助您将普通的提示词优化为更专业、更有效的版本。

## ⚡ 5分钟快速开始

### 1. 配置AI模型

1. 点击右上角的 **⚙️ 模型管理** 按钮
2. 输入您的API密钥（OpenAI、Claude或Gemini）
3. 点击 **测试连接** 确保配置正确
4. 点击 **保存** 完成配置

### 2. 开始优化提示词

1. 在左侧输入框中输入您的原始提示词
2. 选择优化模式（系统提示词优化/用户提示词优化）
3. 选择合适的优化模板
4. 点击 **开始优化** 按钮
5. 等待AI生成优化结果

### 3. 查看和使用结果

1. 在右侧查看优化后的提示词
2. 阅读推理过程了解优化原理
3. 点击 **复制** 按钮复制结果
4. 如需改进，使用 **继续优化** 功能

## 🎯 主要功能

### 📝 提示词优化

- **智能优化** - AI自动改进您的提示词
- **多种模板** - 提供不同场景的优化模板
- **迭代改进** - 可以多次优化直到满意
- **版本管理** - 保存和切换不同版本

### 🤖 模型管理

- **多模型支持** - 支持OpenAI、Claude、Gemini等
- **连接测试** - 确保API配置正确
- **智能切换** - 根据需要选择最适合的模型

### 📚 模板管理

- **丰富模板** - 内置多种优化模板
- **自定义模板** - 创建您自己的优化模板
- **分类管理** - 按类型组织模板

### 📜 历史记录

- **自动保存** - 所有优化记录自动保存
- **快速重用** - 一键重用历史优化结果
- **搜索功能** - 快速找到需要的历史记录

### 💾 数据管理

- **导出备份** - 导出所有数据到本地
- **导入恢复** - 从备份文件恢复数据
- **数据清理** - 清除不需要的数据

## 💡 使用技巧

### 写好原始提示词

- **明确目标** - 清楚说明您想要AI做什么
- **提供背景** - 给出必要的上下文信息
- **具体描述** - 避免模糊和抽象的表达

### 选择合适的模板

- **通用优化** - 适合大多数场景的基础优化
- **专业领域** - 针对特定领域的专业优化
- **创意写作** - 适合创意和文学创作的优化

### 有效使用迭代优化

- **明确改进方向** - 具体说明需要改进的方面
- **逐步完善** - 每次迭代专注一个改进点
- **保存版本** - 保留满意的中间版本

## ⚙️ 个性化设置

### 界面设置

- **主题切换** - 点击右上角切换日间/夜间模式
- **语言切换** - 支持中文/英文界面
- **布局调整** - 界面自适应不同屏幕尺寸

### 使用偏好

- **默认模型** - 设置常用的AI模型
- **常用模板** - 收藏经常使用的模板
- **历史管理** - 定期清理不需要的历史记录

## ❓ 常见问题

### Q: API密钥在哪里获取？

**A:**

- **OpenAI**: 访问 platform.openai.com 注册并获取API密钥
- **Claude**: 访问 console.anthropic.com 获取API密钥
- **Gemini**: 访问 ai.google.dev 获取API密钥

### Q: 优化失败怎么办？

**A:**

1. 检查网络连接是否正常
2. 确认API密钥是否有效且有足够额度
3. 尝试使用不同的模型
4. 简化原始提示词内容

### Q: 如何提高优化质量？

**A:**

1. 提供更详细的原始提示词
2. 选择更适合的优化模板
3. 使用迭代优化功能进行改进
4. 尝试不同的AI模型

### Q: 数据安全吗？

**A:**

- 所有数据存储在您的本地浏览器中
- 不会上传到我们的服务器
- 您可以随时导出或删除数据
- API调用直接连接到AI服务商

## 🔧 故障排除

### 界面问题

- **页面无法加载** - 刷新浏览器或清除缓存
- **按钮无响应** - 检查浏览器是否支持现代Web标准
- **显示异常** - 尝试调整浏览器缩放比例

### 功能问题

- **优化超时** - 检查网络连接，尝试重新优化
- **结果不显示** - 等待更长时间或刷新页面
- **历史记录丢失** - 检查浏览器是否允许本地存储

### 性能问题

- **运行缓慢** - 关闭其他浏览器标签页释放内存
- **内存占用高** - 定期清理历史记录和缓存
- **响应延迟** - 检查网络速度和API服务状态

## 📞 获取帮助

如果您遇到问题或有改进建议：

1. **查看文档** - 详细功能说明请参考用户手册
2. **检查设置** - 确认配置是否正确
3. **重试操作** - 很多问题可以通过重试解决
4. **反馈问题** - 记录具体的错误信息和操作步骤

## 🎉 开始使用

现在您已经了解了基本使用方法，开始体验智能提示词优化的强大功能吧！

记住：好的提示词是与AI有效沟通的关键，让我们的工具帮助您创建更专业、更有效的提示词。
