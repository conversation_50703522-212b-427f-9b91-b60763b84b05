# 待办事项

管理当前和未来的开发任务。

## 🔥 紧急任务

### 本周必须完成

- [ ] [任务描述] - [截止日期] - [负责人]
- [ ] [任务描述] - [截止日期] - [负责人]

### 今日重点

- [ ] [任务描述] - [预计时间]
- [ ] [任务描述] - [预计时间]

## ⭐ 重要任务

### 功能开发

- [ ] [功能名称] - [优先级] - [预计工期]
- [ ] [功能名称] - [优先级] - [预计工期]

### 技术债务

- [ ] [技术债务描述] - [影响程度] - [预计工期]
- [ ] [技术债务描述] - [影响程度] - [预计工期]
- [ ] **重构UI层状态持久化架构** - 影响范围：桌面端，目前UI偏好设置无法保存 - 预计工期：3天
  - **目标**：将UI层对 `useStorage` 的直接依赖，替换为对一个更高阶的 `PreferenceService` 的依赖。
  - **实施**：
    - 1. 创建 `PreferenceService` 及其接口。
    - 2. 在Web端，该服务内部使用 `useStorage` 实现。
    - 3. 在Electron端，创建 `ElectronPreferenceServiceProxy`，通过IPC与主进程通信，由主进程负责读写JSON配置文件。
    - 4. 将 `useTemplateManager`, `ThemeToggleUI`, `LanguageSwitch` 等模块对 `useStorage` 的调用，全部替换为对新服务的调用。

### 文档更新

- [ ] [文档名称] - [更新内容] - [预计时间]
- [ ] [文档名称] - [更新内容] - [预计时间]

## 📋 一般任务

### 优化改进

- [ ] [优化项目] - [预期效果]
- [ ] [优化项目] - [预期效果]

### 学习研究

- [ ] [学习内容] - [学习目标]
- [ ] [学习内容] - [学习目标]

### 工具配置

- [ ] [工具名称] - [配置目标]
- [ ] [工具名称] - [配置目标]

## ✅ 已完成

### 本周完成

- [x] [任务描述] - [完成日期] - [备注]
- [x] [任务描述] - [完成日期] - [备注]

## 🗓️ 未来计划

### 下周计划

- [计划内容] - [预期目标]
- [计划内容] - [预期目标]

### 本月目标

- [月度目标] - [关键里程碑]
- [月度目标] - [关键里程碑]

---

## 📝 使用说明

1. **优先级管理** - 按紧急程度分类任务
2. **时间估算** - 为每个任务估算所需时间
3. **定期更新** - 每日更新进度，每周回顾调整
4. **完成标记** - 及时标记完成的任务并记录备注
