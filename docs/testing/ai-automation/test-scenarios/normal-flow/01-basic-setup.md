# 基础设置正常流程测试

## 📖 测试概述

验证应用基础设置功能的正常工作，包括主题切换、语言切换、界面布局调整等基本配置功能。

## 🎯 测试目标

- 验证主题切换功能正常
- 验证语言切换功能正常
- 验证设置的保存和加载
- 验证响应式布局基本功能

## 📋 前置条件

- [ ] 应用已启动并加载完成
- [ ] 用户界面显示正常
- [ ] 浏览器支持本地存储功能

---

## 🔧 测试步骤

### 步骤1：切换主题模式

**AI执行指导：**

- 使用 `browser_snapshot` 获取页面当前状态
- 查找主题切换按钮（可能显示为"日间模式"、"夜间模式"或相应图标）
- 使用 `browser_click` 点击主题切换按钮
- 再次使用 `browser_snapshot` 检查主题变化

**预期结果：**

- 点击后界面主题立即切换
- 背景色、文字色、按钮样式等发生相应变化
- 按钮图标或文字更新为当前模式的相反状态

**验证点：**

- [ ] 主题切换按钮可见且可点击
- [ ] 点击后界面主题立即变化
- [ ] 按钮状态正确更新
- [ ] 所有界面元素主题一致

---

### 步骤2：切换界面语言

**AI执行指导：**

- 查找语言切换按钮（可能显示为"切换到英文"、"Switch to Chinese"或语言图标）
- 使用 `browser_click` 点击语言切换按钮
- 使用 `browser_snapshot` 检查语言变化
- 验证主要界面元素的语言是否已切换

**预期结果：**

- 点击后界面语言立即切换
- 所有可见文本都更新为目标语言
- 按钮文字更新为切换到另一种语言的提示

**验证点：**

- [ ] 语言切换按钮可见且可点击
- [ ] 点击后界面语言立即变化
- [ ] 主要文本元素正确翻译
- [ ] 按钮和提示文字正确更新

---

### 步骤3：测试设置持久性

**AI执行指导：**

- 先进行主题和语言的切换操作
- 使用 `browser_navigate` 刷新当前页面
- 使用 `browser_snapshot` 检查设置是否保持
- 验证主题和语言状态与刷新前一致

**预期结果：**

- 刷新后主题设置保持不变
- 刷新后语言设置保持不变
- 所有用户偏好设置正确加载

**验证点：**

- [ ] 主题设置在刷新后保持
- [ ] 语言设置在刷新后保持
- [ ] 设置加载速度正常
- [ ] 没有设置丢失或重置

---

### 步骤4：验证响应式布局

**AI执行指导：**

- 使用 `browser_resize` 调整浏览器窗口大小
- 使用 `browser_snapshot` 检查不同尺寸下的布局
- 测试主要功能按钮的可访问性
- 验证内容区域的自适应效果

**测试尺寸：**

```javascript
// 手机尺寸
browser_resize(375, 667);
browser_snapshot();

// 平板尺寸
browser_resize(768, 1024);
browser_snapshot();

// 桌面尺寸
browser_resize(1920, 1080);
browser_snapshot();
```

**预期结果：**

- 界面能够自适应不同窗口尺寸
- 重要功能按钮始终可见和可用
- 文本和内容区域合理调整

**验证点：**

- [ ] 界面布局自适应正常
- [ ] 功能按钮始终可访问
- [ ] 内容显示完整无溢出
- [ ] 用户体验在不同尺寸下良好

---

### 步骤5：测试界面交互反馈

**AI执行指导：**

- 使用 `browser_hover` 悬停在主要按钮上
- 使用 `browser_snapshot` 检查悬停效果
- 使用 `browser_click` 点击各种按钮
- 观察点击反馈和状态变化

**预期结果：**

- 悬停时按钮显示适当的视觉反馈
- 点击时有明确的反馈效果
- 按钮状态变化清晰可见

**验证点：**

- [ ] 悬停效果正常显示
- [ ] 点击反馈及时明确
- [ ] 按钮状态变化正确
- [ ] 交互体验流畅自然

---

## ⚠️ 常见问题检查

### 主题切换问题

- 主题切换不生效
- 部分元素主题不一致
- 切换动画异常

### 语言切换问题

- 语言切换不完整
- 部分文本未翻译
- 切换后布局异常

### 设置保存问题

- 设置不保存
- 刷新后设置丢失
- 本地存储异常

### 响应式问题

- 布局在小屏幕下异常
- 元素重叠或隐藏
- 滚动行为异常

---

## 🤖 AI验证执行模板

```javascript
// 1. 打开应用
browser_navigate("http://localhost:18181/");

// 2. 获取初始状态
browser_snapshot();

// 3. 测试主题切换
browser_click((element = "主题切换按钮"), (ref = "theme_toggle"));
browser_snapshot();

// 4. 测试语言切换
browser_click((element = "语言切换按钮"), (ref = "language_toggle"));
browser_snapshot();

// 5. 测试设置持久性
browser_navigate("http://localhost:18181/"); // 刷新页面
browser_snapshot();

// 6. 测试响应式布局
browser_resize(375, 667); // 手机尺寸
browser_snapshot();
browser_resize(1920, 1080); // 桌面尺寸
browser_snapshot();

// 7. 测试交互反馈
browser_hover((element = "主要按钮"), (ref = "main_button"));
browser_snapshot();
```

**成功标准：**

- 所有基础设置功能正常工作
- 设置能够正确保存和加载
- 响应式布局适应不同屏幕
- 交互反馈及时准确
- 无错误提示或异常状态
