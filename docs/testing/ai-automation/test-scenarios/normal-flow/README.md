# 正常流程测试

## 📖 概述

正常流程测试验证应用的基本功能是否正常工作，这些测试作为回归测试的基准，确保核心功能的稳定性。

## 🎯 测试目标

- 验证主要功能路径的正确性
- 确保用户基本操作流程可用
- 作为回归测试的基准线
- 快速发现核心功能问题

## 📋 测试列表

### 基础功能测试

- **01-basic-setup.md** - 基础设置功能（主题、语言切换等）
- **02-model-management.md** - 模型管理功能（API配置、测试等）
- **03-template-management.md** - 模板管理功能（创建、编辑、删除等）

### 核心功能测试

- **04-prompt-optimization.md** - 系统提示词优化功能（已更新 - 包含结果展示功能测试）✅
- **04b-user-prompt-optimization.md** - 用户提示词优化功能（已更新 - 包含结果展示功能测试）✅
- **05-history-management.md** - 历史记录管理功能
- **06-data-management.md** - 数据管理功能（导入导出等）

### UI交互功能测试

- **07-ui-interaction-features.md** - UI交互功能独立测试（新增）⭐

## 🤖 执行说明

### 执行顺序

建议按照编号顺序执行测试，因为后续测试可能依赖前面的配置：

1. 基础设置 → 2. 模型管理 → 3. 模板管理 → 4. 系统提示词优化 → 4b. 用户提示词优化 → 5. 历史管理 → 6. 数据管理 → 7. UI交互功能

### 执行频率

- **每日回归** - 执行核心功能测试（04-prompt-optimization.md + 04b-user-prompt-optimization.md，现已包含结果展示功能）
- **版本发布前** - 执行全部正常流程测试
- **功能修改后** - 执行相关功能的测试
- **UI修改后** - 执行UI交互功能测试（07-ui-interaction-features.md）

### 成功标准

- 所有操作步骤能够成功执行
- 所有验证点检查通过
- 没有出现错误提示或异常状态
- 用户体验流畅自然

## 📊 测试报告

每次执行后应生成测试报告，包含：

- 执行时间和环境信息
- 各个测试的通过/失败状态
- 发现的问题和改进建议
- 性能指标（如响应时间）

## 🔄 维护说明

- 当功能发生变化时，及时更新对应的测试文档
- 定期检查测试的有效性和准确性
- 根据发现的问题调整测试重点
- 保持测试文档与实际功能的同步
