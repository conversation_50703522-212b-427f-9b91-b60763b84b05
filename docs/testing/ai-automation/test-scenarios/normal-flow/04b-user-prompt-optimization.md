# 用户提示词优化正常流程测试

## 📖 测试概述

验证用户提示词优化功能的基本流程，确保用户能够正常进行用户提示词优化操作。用户提示词优化与系统提示词优化不同，主要用于优化用户的指令，使其更具体、更可执行。

## 🎯 测试目标

- 验证优化模式切换功能（系统提示词 ↔ 用户提示词）
- 确认用户提示词输入和优化流程
- 验证用户提示词优化结果的质量和特点
- 检查模板自动适配功能
- 验证版本管理和迭代功能

## 📋 前置条件

- [ ] 应用已启动并加载完成
- [ ] 至少配置了一个AI模型（OpenAI/Claude/Gemini等）
- [ ] 存在可用的用户提示词优化模板
- [ ] 网络连接正常

---

## 🔧 测试步骤

### 步骤1：切换到用户提示词优化模式

**AI执行指导：**

- 使用 `browser_snapshot` 获取页面当前状态
- 查找优化模式切换按钮："系统提示词优化" 和 "用户提示词优化"
- 使用 `browser_click` 点击"用户提示词优化"按钮

**预期结果：**

- "用户提示词优化"按钮变为选中状态（pressed）
- "系统提示词优化"按钮变为未选中状态
- 界面标题从"原始提示词"变为"User Prompt"
- 输入框提示文字更新为用户提示词相关内容
- 优化模板可能自动切换为适合用户提示词的模板

**验证点：**

- [ ] 模式切换按钮状态正确
- [ ] 界面标题和提示文字正确更新
- [ ] 优化模板自动适配（如切换为"专业优化"等）
- [ ] 测试区域标题更新为"User Prompt Test"

---

### 步骤2：输入用户提示词

**AI执行指导：**

- 使用 `browser_type` 在用户提示词输入框中输入测试内容
- 观察界面状态变化

**测试数据：**

```
典型用户提示词：帮我写一份工作总结
简短指令：翻译这段文字
任务请求：制定一个学习计划
```

**预期结果：**

- 文本框显示已输入的用户提示词
- "Optimize →"按钮变为可点击状态
- 可能出现"Compare"按钮

**验证点：**

- [ ] 用户提示词已成功输入
- [ ] 输入内容完整显示
- [ ] 优化按钮变为可用状态
- [ ] 没有输入错误提示

---

### 步骤3：执行用户提示词优化

**AI执行指导：**

- 使用 `browser_click` 点击"Optimize →"按钮
- 等待优化过程完成（可能需要几秒钟）
- 使用 `browser_wait_for` 等待优化结果显示

**预期结果：**

- 优化按钮显示"Loading..."状态
- 优化完成后显示成功提示
- 右侧区域显示优化后的用户提示词
- 出现版本管理按钮（V1）
- 出现"Continue Optimize"按钮

**验证点：**

- [ ] 优化过程正常启动
- [ ] 优化成功完成，无错误提示
- [ ] 优化结果正确显示
- [ ] 版本管理功能可用

---

### 步骤4：验证用户提示词优化效果

**AI执行指导：**

- 使用 `browser_snapshot` 查看优化结果内容
- 对比原始用户提示词和优化后的结果

**预期结果：**

- 优化后的提示词比原始提示词更具体、更详细
- 包含明确的要求和指导
- 结构化程度更高
- 可执行性更强

**验证点：**

- [ ] 优化效果显著（从简单指令变为详细要求）
- [ ] 优化后内容结构清晰
- [ ] 包含具体的执行指导
- [ ] 内容质量符合用户提示词优化特点

**示例对比：**

```
原始：帮我写一份工作总结
优化后：应包含具体的要求清单，如：
- 总结周期
- 岗位职责
- 主要工作内容
- 工作成果
- 亮点与创新
- 不足之处
- 经验教训
- 未来展望
- 格式要求
- 提交截止日期
```

---

### 步骤5：测试模式切换保持功能

**AI执行指导：**

- 使用 `browser_click` 切换回"系统提示词优化"模式
- 再次切换回"用户提示词优化"模式
- 观察内容是否保持

**预期结果：**

- 模式切换流畅，无延迟
- 已输入的内容和优化结果保持不变
- 界面元素正确更新
- 模板自动适配

**验证点：**

- [ ] 模式切换功能正常
- [ ] 内容不会因切换而丢失
- [ ] 界面状态正确更新
- [ ] 模板自动切换正确

---

### 步骤6：测试迭代优化功能

**AI执行指导：**

- 使用 `browser_click` 点击"Continue Optimize"按钮
- 在迭代优化界面输入优化方向
- 执行迭代优化

**测试数据：**

```
迭代要求：请增加更多关于时间管理和具体格式的要求
```

**预期结果：**

- 迭代优化界面正确打开
- 可以输入优化方向
- 迭代优化成功执行
- 生成V2版本
- 版本切换功能正常

**验证点：**

- [ ] 迭代优化界面正常打开
- [ ] 优化方向输入成功
- [ ] 迭代优化执行成功
- [ ] V2版本正确生成
- [ ] 版本切换功能正常

---

### 步骤7：测试结果展示功能 ⭐ 新增

**AI执行指导：**

- 在用户提示词优化完成后，继续测试结果展示相关功能
- 验证用户在获得优化结果后的各种操作体验

#### 7.1 视图切换功能测试

**AI执行指导：**

- 使用 `browser_click` 点击"Source"按钮
- 使用 `browser_snapshot` 验证内容变为Markdown源码格式
- 使用 `browser_click` 点击"Render"按钮
- 使用 `browser_snapshot` 验证内容变为HTML渲染格式

**预期结果：**

- 渲染视图：显示格式化的用户提示词优化结果
- 源码视图：显示原始的Markdown文本格式
- 按钮状态正确更新（当前视图按钮disabled）

**验证点：**

- [ ] 视图切换响应正常
- [ ] 内容格式正确转换
- [ ] 按钮状态正确更新
- [ ] 内容完整性保持

#### 7.2 复制功能测试

**AI执行指导：**

- 使用 `browser_click` 点击"Copy"按钮
- 观察是否出现成功提示

**预期结果：**

- 出现"Copied to clipboard"提示
- 提示自动消失或可手动关闭

**验证点：**

- [ ] 复制按钮响应正常
- [ ] 成功提示正确显示
- [ ] 用户反馈及时清晰

#### 7.3 全屏查看功能测试

**AI执行指导：**

- 使用 `browser_click` 点击"Fullscreen"按钮
- 使用 `browser_snapshot` 验证全屏界面
- 测试全屏模式下的视图切换
- 使用 `browser_click` 关闭全屏

**预期结果：**

- 打开独立的全屏内容查看器
- 全屏模式有完整的功能控制
- 可以正常关闭回到原界面

**验证点：**

- [ ] 全屏界面正确打开
- [ ] 全屏模式功能完整
- [ ] 视图控制正常工作
- [ ] 关闭功能正常

#### 7.4 智能对比功能测试

**AI执行指导：**

- 使用 `browser_click` 点击"Compare"按钮
- 使用 `browser_snapshot` 观察对比显示效果

**预期结果：**

- 智能识别原始用户提示词和优化结果的差异
- 分段显示不同部分（原始、共同、优化扩展）
- Compare按钮状态更新为disabled

**验证点：**

- [ ] 对比模式正确激活
- [ ] 文本差异正确识别
- [ ] 分段显示清晰
- [ ] 按钮状态正确更新

#### 7.5 展开编辑功能测试

**AI执行指导：**

- 使用 `browser_click` 点击"Expand"按钮
- 使用 `browser_snapshot` 验证全屏编辑界面
- 测试输入功能
- 使用 `browser_click` 关闭编辑界面

**预期结果：**

- 打开全屏编辑模式
- 有独立的编辑窗口和关闭按钮
- 输入功能正常工作

**验证点：**

- [ ] 全屏编辑界面正确打开
- [ ] 输入功能正常
- [ ] 关闭功能正常
- [ ] 内容保持一致

---

## 🎯 测试重点

### 核心功能验证

1. **模式切换功能**：系统提示词优化 ↔ 用户提示词优化
2. **模板自动适配**：不同模式自动选择合适的优化模板
3. **优化效果差异**：用户提示词优化应产生指令优化结果，而非内容生成

### 用户体验验证

1. **界面一致性**：两种模式的操作流程应该一致
2. **内容保持**：模式切换不应丢失已有工作成果
3. **反馈及时性**：优化过程应有清晰的状态反馈

### 质量验证

1. **优化质量**：用户提示词优化应使指令更具体、更可执行
2. **版本管理**：支持多版本管理和切换
3. **迭代功能**：支持基于反馈的迭代优化

---

## 📊 成功标准

### A级标准（必须通过）

- [ ] 模式切换功能100%正常
- [ ] 用户提示词优化功能100%正常
- [ ] 优化结果质量符合预期
- [ ] 版本管理功能100%正常

### B级标准（应该通过）

- [ ] 模板自动适配功能正常
- [ ] 迭代优化功能正常
- [ ] 界面交互流畅
- [ ] 内容保持功能正常

### C级标准（可以接受的问题）

- [ ] 优化速度稍慢（10秒内完成可接受）
- [ ] 界面元素轻微延迟更新
- [ ] 非关键功能的小问题

---

## 🐛 常见问题排查

### 模式切换问题

- 检查按钮状态是否正确更新
- 确认界面元素是否正确切换
- 验证模板是否自动适配

### 优化功能问题

- 确认模型配置是否正确
- 检查网络连接是否正常
- 验证输入内容是否符合要求

### 结果显示问题

- 检查优化结果是否完整显示
- 确认版本管理功能是否正常
- 验证内容格式是否正确

---

## 📝 测试记录模板

```
测试执行时间：____
测试执行人：____
测试环境：____

步骤1 - 模式切换：□ 通过 □ 失败
步骤2 - 用户提示词输入：□ 通过 □ 失败
步骤3 - 优化执行：□ 通过 □ 失败
步骤4 - 优化效果验证：□ 通过 □ 失败
步骤5 - 模式切换保持：□ 通过 □ 失败
步骤6 - 迭代优化：□ 通过 □ 失败

总体评分：____/100
发现问题：____
改进建议：____
```
