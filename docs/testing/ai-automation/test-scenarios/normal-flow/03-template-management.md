# 模板管理正常流程测试

## 📖 测试概述

验证模板管理功能的基本流程，确保用户能够正常查看、创建、编辑和管理优化模板。

## 🎯 测试目标

- 验证模板管理界面正常打开
- 验证模板浏览和查看功能
- 验证模板创建和编辑功能
- 验证模板分类和管理功能
- 验证内置模板语言切换功能

## 📋 前置条件

- [ ] 应用已启动并加载完成
- [ ] 用户界面显示正常
- [ ] 了解基本的模板概念

---

## 🔧 测试步骤

### 步骤1：打开模板管理器

**AI执行指导：**

- 使用 `browser_snapshot` 获取页面当前状态
- 查找包含"📝"图标和"功能提示词"文字的按钮
- 使用 `browser_click` 点击该按钮

**预期结果：**

- 弹出模板管理对话框
- 对话框标题显示"模板管理"或"功能提示词"
- 界面显示现有模板列表和管理选项

**验证点：**

- [ ] 模板管理弹窗已显示
- [ ] 弹窗标题正确显示
- [ ] 可以看到模板列表或分类选项
- [ ] 界面包含添加、编辑等操作按钮

---

### 步骤2：浏览现有模板

**AI执行指导：**

- 使用 `browser_snapshot` 查看模板列表内容
- 查找模板分类标签或过滤选项
- 点击不同分类查看模板变化
- 选择一个模板项目查看详情

**预期结果：**

- 模板按分类正确显示
- 每个模板显示名称、描述等基本信息
- 点击分类标签能够过滤显示相应模板
- 选择模板能够查看详细内容

**验证点：**

- [ ] 模板列表正确显示
- [ ] 分类过滤功能正常
- [ ] 模板信息显示完整
- [ ] 模板选择功能正常

---

### 步骤3：查看模板详情

**AI执行指导：**

- 选择一个现有模板
- 查看模板的详细信息
- 检查模板内容、描述、类型等信息
- 测试模板预览功能（如果有）

**预期结果：**

- 模板详细信息完整显示
- 模板内容格式正确
- 模板类型和描述准确
- 预览功能正常工作（如果支持）

**验证点：**

- [ ] 模板详情正确显示
- [ ] 模板内容格式正确
- [ ] 类型和描述准确
- [ ] 预览功能正常（如果有）

---

### 步骤4：创建新模板（模拟）

**AI执行指导：**

- 查找"添加"、"新建"、"创建"等按钮
- 使用 `browser_click` 点击添加按钮
- 选择模板类型（如果有选择器）
- 检查模板创建界面的布局

**预期结果：**

- 打开模板创建/编辑界面
- 显示模板类型选择选项
- 提供名称、描述、内容等输入字段
- 界面布局清晰，字段标识明确

**验证点：**

- [ ] 模板创建界面正确打开
- [ ] 模板类型选择功能可用
- [ ] 所有必要的输入字段存在
- [ ] 界面响应正常

---

### 步骤5：填写模板信息（模拟）

**AI执行指导：**

- 查找模板名称输入框
- 使用 `browser_type` 输入测试名称
- 查找描述输入框并输入描述
- 查找模板内容编辑区域

**测试数据：**

```
模板名称：测试模板
模板描述：这是一个用于测试的模板
模板类型：系统优化（或用户优化）
```

**预期结果：**

- 名称和描述输入正常
- 模板类型选择正常
- 内容编辑区域可用
- 输入验证正常工作

**验证点：**

- [ ] 名称输入功能正常
- [ ] 描述输入功能正常
- [ ] 类型选择功能正常
- [ ] 输入验证正确

---

### 步骤6：编写模板内容（模拟）

**AI执行指导：**

- 查找模板内容编辑区域（通常是大的文本框）
- 使用 `browser_type` 输入测试模板内容
- 查找模式切换选项（简单/高级）
- 检查编辑器的功能和响应

**测试数据：**

```
模板内容：
你是一个专业的提示词优化专家。
请帮助用户优化以下提示词：
{原始提示词}

优化要求：
1. 使提示词更加清晰明确
2. 增加必要的上下文信息
3. 改进语言表达
```

**预期结果：**

- 模板内容正确输入到编辑区域
- 编辑器支持多行文本输入
- 模式切换功能正常（如果存在）
- 编辑器响应流畅

**验证点：**

- [ ] 模板内容成功输入
- [ ] 编辑器功能正常
- [ ] 模式切换工作正常（如果有）
- [ ] 编辑器响应流畅

---

### 步骤7：保存模板（模拟）

**AI执行指导：**

- 检查模板信息的完整性
- 查找"保存"、"确定"、"提交"等按钮
- 使用 `browser_click` 点击保存按钮
- 等待保存完成并检查结果

**预期结果：**

- 保存按钮响应正常
- 显示保存成功的提示信息
- 返回模板列表界面
- 新创建的模板出现在相应分类中

**验证点：**

- [ ] 保存按钮功能正常
- [ ] 显示保存成功提示
- [ ] 返回模板列表界面
- [ ] 新模板正确显示在列表中

---

### 步骤8：编辑现有模板（模拟）

**AI执行指导：**

- 在模板列表中选择一个现有模板
- 查找"编辑"按钮或尝试双击模板
- 检查编辑界面是否正确加载
- 验证现有信息是否正确显示

**预期结果：**

- 打开模板编辑界面
- 现有模板信息正确加载到编辑器中
- 可以修改所有可编辑字段
- 编辑界面功能完整

**验证点：**

- [ ] 编辑界面正确打开
- [ ] 现有信息正确加载
- [ ] 修改功能正常工作
- [ ] 编辑界面功能完整

---

### 步骤9：测试模板分类

**AI执行指导：**

- 查找模板分类标签或过滤器
- 点击不同的分类选项
- 观察模板列表的变化
- 验证分类过滤的准确性

**预期结果：**

- 分类标签清晰可见
- 点击分类能够过滤模板
- 过滤结果准确
- 分类切换流畅

**验证点：**

- [ ] 分类标签功能正常
- [ ] 分类过滤准确
- [ ] 过滤结果正确
- [ ] 分类切换流畅

---

### 步骤10：测试内置模板语言切换

**AI执行指导：**

- 查找模板管理界面中的语言切换按钮（通常显示"中文"或"EN"）
- 使用 `browser_click` 点击语言切换按钮
- 观察内置模板名称的变化
- 验证模板内容语言是否相应改变

**预期结果：**

- 语言切换按钮响应正常
- 内置模板名称从中文切换为英文（或反之）
- 模板内容语言相应改变
- 界面显示切换成功的提示

**验证点：**

- [ ] 语言切换按钮功能正常
- [ ] 内置模板名称语言正确切换
- [ ] 模板内容语言相应改变
- [ ] 显示切换成功提示

**注意事项：**

- 内置模板语言切换不同于界面语言切换
- 内置模板语言影响模板名称和内容的显示语言
- 切换后应该能看到如"通用优化"变为"General Optimization"

---

## ⚠️ 常见问题检查

### 界面显示问题

- 模板管理弹窗无法打开
- 模板列表显示异常
- 编辑界面布局错乱
- 分类标签显示问题

### 功能操作问题

- 模板创建功能异常
- 编辑功能不可用
- 保存操作失败
- 分类过滤不准确

### 数据管理问题

- 模板信息丢失
- 保存后内容异常
- 分类信息错误
- 模板加载失败

---

## 🤖 AI验证执行模板

```javascript
// 1. 打开应用
browser_navigate("http://localhost:18181/");

// 2. 获取初始状态
browser_snapshot();

// 3. 打开模板管理
browser_click((element = "模板管理按钮"), (ref = "template_management_button"));
browser_snapshot();

// 4. 浏览现有模板
browser_snapshot();

// 5. 查看模板详情
browser_click((element = "模板项"), (ref = "template_item"));
browser_snapshot();

// 6. 创建新模板
browser_click((element = "添加模板按钮"), (ref = "add_template_button"));
browser_snapshot();

// 7. 填写模板信息
browser_type(
  (element = "模板名称输入框"),
  (ref = "template_name_input"),
  (text = "测试模板"),
);
browser_type(
  (element = "模板描述输入框"),
  (ref = "template_desc_input"),
  (text = "测试描述"),
);
browser_snapshot();

// 8. 编写模板内容
browser_type(
  (element = "模板内容编辑器"),
  (ref = "template_content_editor"),
  (text = "测试模板内容"),
);
browser_snapshot();

// 9. 保存模板
browser_click((element = "保存按钮"), (ref = "save_button"));
browser_snapshot();

// 10. 测试分类过滤
browser_click((element = "分类标签"), (ref = "category_tab"));
browser_snapshot();

// 11. 测试内置模板语言切换
browser_click(
  (element = "内置模板语言切换按钮"),
  (ref = "builtin_language_toggle"),
);
browser_snapshot();
```

**成功标准：**

- 模板管理界面正常打开和操作
- 模板浏览和查看功能正常
- 模板创建和编辑功能正常
- 模板分类和过滤功能正常
- 内置模板语言切换功能正常
- 所有操作响应及时准确
- 无错误提示或异常状态
