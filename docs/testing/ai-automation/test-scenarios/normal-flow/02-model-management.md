# 模型管理正常流程测试

## 📖 测试概述

验证模型管理功能的基本流程，确保用户能够正常配置和管理AI模型。

## 🎯 测试目标

- 验证模型管理界面正常打开
- 验证API密钥配置功能
- 验证模型连接测试功能
- 验证配置保存和加载

## 📋 前置条件

- [ ] 应用已启动并加载完成
- [ ] 用户界面显示正常
- [ ] 网络连接正常
- [ ] 已准备好测试用的API密钥（可选）

---

## 🔧 测试步骤

### 步骤1：打开模型管理器

**AI执行指导：**

- 使用 `browser_snapshot` 获取页面当前状态
- 查找包含"⚙️"图标和"模型管理"文字的按钮
- 使用 `browser_click` 点击该按钮

**预期结果：**

- 弹出模型管理对话框
- 对话框标题显示"模型配置"或类似文字
- 界面显示各种模型的配置选项

**验证点：**

- [ ] 模型管理弹窗已显示
- [ ] 弹窗标题正确显示
- [ ] 可以看到OpenAI、Claude、Gemini等模型选项
- [ ] 界面布局清晰，功能区域明确

---

### 步骤2：查看模型配置界面

**AI执行指导：**

- 使用 `browser_snapshot` 查看当前弹窗内容
- 检查各个模型的配置区域
- 查看API密钥输入框和其他配置选项
- 检查按钮和控件的可用性

**预期结果：**

- 每个模型都有独立的配置区域
- API密钥输入框清晰可见
- 模型选择下拉框可用
- 测试连接按钮可见

**验证点：**

- [ ] 模型配置区域布局合理
- [ ] API密钥输入框可见
- [ ] 模型选择选项可用
- [ ] 测试连接按钮可见

---

### 步骤3：配置OpenAI模型（模拟）

**AI执行指导：**

- 找到标有"OpenAI"的配置区域
- 查找API密钥输入框
- 使用 `browser_type` 输入测试密钥（如果允许）
- 查找模型选择下拉框并检查选项

**测试数据：**

```
API密钥：test_api_key_for_testing
模型选择：GPT-4 或 GPT-3.5-turbo
```

**预期结果：**

- API密钥输入框接受输入
- 模型选择下拉框显示可用选项
- 配置界面响应正常

**验证点：**

- [ ] API密钥输入功能正常
- [ ] 模型选择功能可用
- [ ] 配置界面响应正常
- [ ] 没有格式错误提示

---

### 步骤4：测试连接功能（模拟）

**AI执行指导：**

- 查找"测试连接"、"验证"、"测试"等按钮
- 使用 `browser_click` 点击测试按钮
- 使用 `browser_wait_for` 等待测试完成
- 检查测试结果的显示状态

**预期结果：**

- 测试按钮响应点击
- 显示测试进行中的状态（如加载图标）
- 测试完成后显示结果（成功或失败）
- 结果信息清晰明确

**验证点：**

- [ ] 测试按钮功能正常
- [ ] 测试过程状态清晰
- [ ] 测试结果明确显示
- [ ] 错误信息有用（如果测试失败）

---

### 步骤5：保存配置

**AI执行指导：**

- 检查所有配置信息
- 查找"保存"、"确定"、"应用"等按钮
- 使用 `browser_click` 点击保存按钮
- 等待保存操作完成的确认

**预期结果：**

- 保存按钮响应正常
- 显示保存成功的提示信息
- 弹窗状态更新或关闭
- 配置信息得到保存

**验证点：**

- [ ] 保存按钮功能正常
- [ ] 显示保存成功提示
- [ ] 弹窗状态正确更新
- [ ] 配置保存成功

---

### 步骤6：验证配置生效

**AI执行指导：**

- 关闭模型管理弹窗（如果还打开）
- 使用 `browser_snapshot` 检查主界面状态
- 查找模型选择下拉框
- 验证新配置的模型是否出现在选项中

**预期结果：**

- 主界面模型选择器包含配置的模型
- 模型名称正确显示
- 状态指示器显示为可用
- 可以正常选择和切换模型

**验证点：**

- [ ] 配置的模型出现在选择器中
- [ ] 模型名称显示正确
- [ ] 状态指示器显示正常
- [ ] 模型选择功能正常工作

---

### 步骤7：重新打开验证持久性

**AI执行指导：**

- 重新打开模型管理界面
- 检查之前的配置是否保持
- 验证API密钥和模型选择是否保存
- 测试配置的完整性

**预期结果：**

- 之前的配置正确加载
- API密钥状态正确显示
- 模型选择保持不变
- 所有设置持久保存

**验证点：**

- [ ] 配置正确加载
- [ ] API密钥状态正确
- [ ] 模型选择保持
- [ ] 设置持久保存

---

## ⚠️ 常见问题检查

### 界面显示问题

- 弹窗无法打开
- 配置区域显示异常
- 按钮状态错误
- 布局错乱

### 配置功能问题

- API密钥无法输入
- 模型选择不可用
- 测试连接失败
- 保存功能异常

### 数据持久性问题

- 配置无法保存
- 刷新后配置丢失
- 设置加载失败
- 本地存储异常

---

## 🤖 AI验证执行模板

```javascript
// 1. 打开应用
browser_navigate("http://localhost:18181/");

// 2. 获取初始状态
browser_snapshot();

// 3. 打开模型管理
browser_click((element = "模型管理按钮"), (ref = "model_management_button"));
browser_snapshot();

// 4. 检查配置界面
browser_snapshot();

// 5. 配置OpenAI模型（如果允许）
browser_type(
  (element = "API密钥输入框"),
  (ref = "api_key_input"),
  (text = "test_api_key"),
);
browser_snapshot();

// 6. 测试连接（如果可以）
browser_click((element = "测试连接按钮"), (ref = "test_connection_button"));
browser_wait_for((time = 5));
browser_snapshot();

// 7. 保存配置
browser_click((element = "保存按钮"), (ref = "save_button"));
browser_snapshot();

// 8. 关闭弹窗
browser_press_key("Escape");

// 9. 验证配置生效
browser_snapshot();

// 10. 重新打开验证持久性
browser_click((element = "模型管理按钮"), (ref = "model_management_button"));
browser_snapshot();
```

**成功标准：**

- 模型管理界面正常打开和关闭
- 配置功能正常工作
- 测试连接功能响应正常
- 配置能够正确保存和加载
- 主界面正确反映配置变化
- 无错误提示或异常状态
