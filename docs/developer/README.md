# 开发者文档

欢迎参与Prompt Optimizer的开发！这里包含了所有开发相关的技术文档。

## 🚀 快速开始

- 快速开始指南（待创建） - 开发环境搭建和项目启动
- [技术开发指南](./technical-development-guide.md) - 完整的技术栈和开发规范
- [项目结构](./project-structure.md) - 项目文件和目录组织说明
- [AI开发流程规范](./ai-development-workflow.md) - AI辅助开发的标准化流程
- [通用开发经验](./general-experience.md) - 项目开发中的通用经验与最佳实践

## 📱 平台开发指南

### 桌面端

- [桌面开发指南](./desktop-developer-guide.md) - Electron桌面应用开发

### Web端

- Web开发指南（待创建） - Web应用开发说明

### 浏览器插件

- 插件开发指南（待创建） - Chrome扩展开发

## 📚 API文档

- [核心API文档](./api/core-api.md)（待创建） - @prompt-optimizer/core包API参考

## 🏗️ 架构文档

- [架构概览](./architecture/overview.md)（待创建） - 系统整体架构
- [设计模式](./architecture/design-patterns.md)（待创建） - 项目中使用的设计模式

## 🔧 故障排查

- [通用排查清单](./troubleshooting/general-checklist.md) - 常见问题的排查步骤
- [排查指南索引](./troubleshooting/README.md)（待创建） - 所有排查文档的索引

## 🤝 贡献指南

- 贡献指南（待创建） - 如何参与项目开发
- 代码规范（在技术开发指南中） - 编码标准和最佳实践
- 提交规范（待创建） - Git提交消息规范

## 📋 开发流程

- [开发任务清单](./todo.md) - 按功能模块和优先级组织的任务列表
- 开发流程（待创建） - 从需求到发布的完整流程
- 测试指南（待创建） - 单元测试和集成测试
- 发布流程（待创建） - 版本发布和部署流程
