# 开发草稿本

记录当前开发任务的进展和思考。

## 当前任务

### [任务名称] - [开始日期]

**目标**: [具体目标描述]
**状态**: 进行中

#### 计划步骤

[ ] 1. 需求分析
[ ] 2. 技术方案设计  
[ ] 3. 功能实现
[ ] 4. 测试验证
[ ] 5. 文档更新

#### 进展记录

- [日期] [具体进展描述]
- [日期] [遇到的问题和解决方案]

#### 重要发现

- [记录重要的技术发现或经验]

---

## 历史任务

### [已完成任务名称] - [完成日期] ✅

**总结**: [简要总结]
**经验**: [重要经验提取]

---

## 待办事项

### 紧急

- [ ] [紧急任务1]
- [ ] [紧急任务2]

### 重要

- [ ] [重要任务1]
- [ ] [重要任务2]

### 一般

- [ ] [一般任务1]
- [ ] [一般任务2]

---

## 问题记录

### 未解决

- [问题描述] - [发现日期]

### 已解决

- [问题描述] - [解决方案] - [解决日期]

---

## 备注

[其他需要记录的信息]

## 任务：核心服务接口隔离重构 - 2025-07-03

### 目标

逐项检查 `ui` 模块对 `core` 模块的引用，确保所有调用都通过接口（如 `IModelManager`）而不是具体实现类（如 `ModelManager`）。统一 Web 和 Desktop 版的调用规范，修复因此产生的 IPC 通信链路问题。

### 计划步骤

_见 `todo.md`_

### 进展记录

- **2025-07-03**: **里程碑完成：核心管理器重构**
  - **成果**: 成功重构 `ModelManager` 和 `HistoryManager`。通过在 `useAppInitializer.ts` 中创建适配器，强制 UI 层仅通过接口调用。
  - **发现**: Web 应用能工作的"捷径"（直接调用实例）是导致 Desktop 版（多进程IPC）失败的根源。架构上必须在初始化源头屏蔽实现细节。
  - **状态**: 已完成对 `IModelManager`, `IHistoryManager` 及其对应实现、代理和 IPC 链路的更新。

### 问题记录

_暂无_

### 里程碑

- [x] 重构 ModelManager
- [x] 重构 HistoryManager
- [ ] 重构 TemplateManager
- [ ] 重构 LLMService
- [ ] 重构 PromptService
- [ ] 重构其他服务
- [ ] 完成最终测试
