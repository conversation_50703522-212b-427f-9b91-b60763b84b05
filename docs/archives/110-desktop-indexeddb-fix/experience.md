# "Desktop IndexedDB修复"任务经验总结

## 核心经验

### 架构设计

- **强制明确性优于便利性**: 本次任务的核心是删除了`createDefault()`这类便利方法。这强制开发者在创建服务时必须明确指定存储类型，从而避免了在Electron等不适宜的环境下意外创建IndexedDB。这是一个重要的架构原则，可以防止隐蔽的、由环境带来的副作用。
- **避免模块级副作用**: 我们发现，在`factory.ts`等模块的顶层作用域创建实例（如存储提供者）是一个巨大的隐患。模块在被导入时不应该执行任何具有副作用的实质性操作。所有实例化都应通过明确的函数调用和依赖注入来完成。

### 调试与排查

- **警惕历史遗留数据**: 这是一个关键教训。即使代码已经修复，残留在浏览器中的IndexedDB数据也可能导致应用行为异常，从而掩盖修复的真实效果。在处理与持久化数据相关的问题时，必须将"清理历史数据"作为验证步骤的一部分。
- **避免过度修复**: 在排查问题的初期，我们曾在代码中添加了一些复杂的环境检查和警告逻辑。虽然初衷是好的，但这增加了代码的复杂度。最终，通过更根本的架构修复（删除`createDefault`），这些复杂的逻辑变得多余。这提醒我们，在修复后要及时审视并清理过程中添加的临时代码或过度防御性代码。

## 具体避坑指南

- **问题**: 在Electron渲染进程中不应出现IndexedDB。
- **后果**: 违反了桌面端的核心架构（数据应由主进程统一管理），可能导致数据不一致和意外的磁盘I/O。
- **正确做法**: 渲染进程应完全通过IPC代理与主进程通信来操作数据，不应直接创建任何存储实例。所有存储相关的逻辑都应被封装在主进程中。

- **问题**: 便利的工厂方法（如`createDefault()`）可能隐藏环境依赖。
- **后果**: 导致模块在不同环境下行为不一致，增加了调试难度。
- **正确做法**: 移除此类隐式创建实例的方法。强制使用依赖注入，让所有依赖关系都变得明确、可控和易于测试。
