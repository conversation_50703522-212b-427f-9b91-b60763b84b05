---
description: 任务执行过程的进度更新
alwaysApply: false
---
# 工作流程2：任务执行

迭代开发，更新进度，记录发现和问题。

## 🎯 流程目标

- 按计划执行开发任务
- 及时更新任务进度
- 记录重要发现和经验
- 保持与用户的有效沟通

## 📋 前置条件

### 输入要求
- 任务已通过[工作流程1：任务初始化](./1-task-initialization.md)完成设置
- workspace包含具体的任务描述和计划
- 技术方案已经确定

### 环境检查
- scratchpad.md包含具体任务（非模板内容）
- 有明确的计划步骤列表
- 开发环境已准备就绪

## 🔄 执行步骤

### 1. 步骤执行循环

#### 1.1 开始新步骤 ⚠️ 必须反馈
**操作内容**：
- 选择下一个待执行的步骤
- 确认前置条件已满足
- 评估当前步骤的复杂度

**⚠️ 反馈要求**：
```typescript
await mcp_feedback_enhanced({
  summary: "准备执行步骤[N]：[步骤描述]。预计需要[时间]，主要工作包括[工作内容]。开始执行？",
  project_directory: "."
})
```

**反馈内容细化**：
- 明确说明当前步骤的具体目标
- 列出需要完成的主要工作项
- 说明预期的输出和验收标准
- 提及可能遇到的技术难点

#### 1.2 实施开发工作
**操作内容**：
- 编写代码
- 运行测试
- 验证功能
- 修复问题

**实时记录**：在scratchpad.md中更新进展记录
```markdown
#### 进展记录
- [时间] 开始执行步骤[N]：[步骤名称]
- [时间] 完成[具体工作]，遇到[问题描述]
- [时间] 解决方案：[解决方法]
```

#### 1.3 步骤完成确认 ⚠️ 重要里程碑需反馈
**操作内容**：
- 标记步骤完成：`[x] 1. [已完成步骤]`
- 记录实际结果和发现
- 评估对后续步骤的影响

**⚠️ 反馈触发条件**：
- 完成重要里程碑
- 发现重大技术问题  
- 需要调整后续计划
- 超出预期时间50%以上

**反馈格式**：
```typescript
await mcp_feedback_enhanced({
  summary: "步骤[N]已完成：[成果描述]。发现[重要发现]。后续计划[是否需要调整]。",
  project_directory: "."
})
```

**反馈内容细化**：
- 详细描述实际完成的功能和效果
- 说明与预期目标的差异（如有）
- 总结重要的技术发现或经验
- 评估对后续步骤的影响和调整建议

### 2. 问题处理流程

#### 2.1 遇到技术问题 ⚠️ 必须反馈
**操作内容**：
- 详细分析问题原因
- 研究可能的解决方案
- 评估解决方案的影响

**⚠️ 反馈要求**：
```typescript
await mcp_feedback_enhanced({
  summary: "遇到技术问题：[问题描述]。可能原因：[分析]。建议解决方案：[方案1/方案2]。请选择处理方式？",
  project_directory: "."
})
```

**反馈内容细化**：
- 清晰描述问题的具体表现和影响范围
- 提供详细的根因分析
- 给出2-3个可行的解决方案，包括优缺点
- 说明各方案的时间成本和风险评估

#### 2.2 记录问题和解决方案
**操作内容**：
在scratchpad.md中记录：
```markdown
## 问题记录

### [问题标题] - [发现日期]
**问题描述**: [详细描述]
**原因分析**: [根本原因]
**解决方案**: [采用的解决方法]
**经验总结**: [避坑要点]
```

### 3. 经验记录

#### 3.1 重要发现记录
**操作内容**：
在scratchpad.md中实时记录：
```markdown
#### 重要发现
- [时间] [技术发现或最佳实践]
- [时间] [需要注意的细节或陷阱]
- [时间] [可复用的代码模式或工具]
```

#### 3.2 通用经验提取
**操作内容**：
- 识别可复用的经验
- 在experience.md中分类记录
- 避免重复造轮子

### 4. 进度管理

#### 4.1 定期进度检查
**检查频率**：
- 简单任务：每完成1-2个步骤
- 复杂任务：每2-4小时
- 遇到阻塞：立即检查

**检查内容**：
- 当前进度vs计划进度
- 遇到的问题和阻塞
- 后续计划是否需要调整

#### 4.2 计划调整 ⚠️ 必须反馈
**触发条件**：
- 发现新的技术问题
- 需求发生变化
- 时间估算偏差较大
- 发现更好的解决方案

**⚠️ 反馈要求**：
```typescript
await mcp_feedback_enhanced({
  summary: "计划需要调整。原因：[调整原因]。建议将[原计划]调整为[新计划]。预计影响：[时间/范围变化]。",
  project_directory: "."
})
```

**反馈内容细化**：
- 详细说明需要调整的具体原因
- 对比原计划和新计划的差异
- 量化评估时间、资源、范围的变化
- 说明调整后的风险和收益

## ✅ 阶段完成标准

### 单个步骤完成
- [ ] 功能按预期实现
- [ ] 测试验证通过
- [ ] 代码质量符合标准
- [ ] 问题和发现已记录
- [ ] 步骤状态已更新

### 重要里程碑完成
- [ ] 用户已确认阶段成果
- [ ] 重要经验已记录
- [ ] 后续计划已确认
- [ ] 必要的文档已更新

## 🔄 流程转换

### 继续执行
- 还有未完成步骤 → 继续执行循环
- 遇到阻塞问题 → 进入问题处理流程
- 需要调整计划 → 更新计划并获得确认

### 转向归档
- 所有主要步骤完成 → [工作流程3：任务归档](./3-task-archiving.md)
- 任务暂停/取消 → 整理现有成果后归档
- workspace内容过多 → 阶段性归档，重新初始化

## 🚨 异常处理

### 长时间无进展
**处理步骤**：
1. 分析阻塞原因
2. 寻求外部帮助或改变方法
3. 考虑降低任务范围
4. 与用户讨论替代方案

### 频繁变更需求
**处理步骤**：
1. 暂停当前工作
2. 重新进行需求分析
3. 评估已完成工作的价值
4. 决定是否需要重新初始化

### 技术方案证明不可行
**处理步骤**：
1. 立即停止当前实施
2. 详细分析失败原因
3. 研究替代技术方案
4. 与用户讨论后续计划

---

**关键成功要素**: 持续沟通 + 及时调整 + 详细记录 # 工作流程2：任务执行

迭代开发，更新进度，记录发现和问题。

## 🎯 流程目标

- 按计划执行开发任务
- 及时更新任务进度
- 记录重要发现和经验
- 保持与用户的有效沟通

## 📋 前置条件

### 输入要求
- 任务已通过[工作流程1：任务初始化](./1-task-initialization.md)完成设置
- workspace包含具体的任务描述和计划
- 技术方案已经确定

### 环境检查
- scratchpad.md包含具体任务（非模板内容）
- 有明确的计划步骤列表
- 开发环境已准备就绪

## 🔄 执行步骤

### 1. 步骤执行循环

#### 1.1 开始新步骤 ⚠️ 必须反馈
**操作内容**：
- 选择下一个待执行的步骤
- 确认前置条件已满足
- 评估当前步骤的复杂度

**⚠️ 反馈要求**：
```typescript
await mcp_feedback_enhanced({
  summary: "准备执行步骤[N]：[步骤描述]。预计需要[时间]，主要工作包括[工作内容]。开始执行？",
  project_directory: "."
})
```

**反馈内容细化**：
- 明确说明当前步骤的具体目标
- 列出需要完成的主要工作项
- 说明预期的输出和验收标准
- 提及可能遇到的技术难点

#### 1.2 实施开发工作
**操作内容**：
- 编写代码
- 运行测试
- 验证功能
- 修复问题

**实时记录**：在scratchpad.md中更新进展记录
```markdown
#### 进展记录
- [时间] 开始执行步骤[N]：[步骤名称]
- [时间] 完成[具体工作]，遇到[问题描述]
- [时间] 解决方案：[解决方法]
```

#### 1.3 步骤完成确认 ⚠️ 重要里程碑需反馈
**操作内容**：
- 标记步骤完成：`[x] 1. [已完成步骤]`
- 记录实际结果和发现
- 评估对后续步骤的影响

**⚠️ 反馈触发条件**：
- 完成重要里程碑
- 发现重大技术问题  
- 需要调整后续计划
- 超出预期时间50%以上

**反馈格式**：
```typescript
await mcp_feedback_enhanced({
  summary: "步骤[N]已完成：[成果描述]。发现[重要发现]。后续计划[是否需要调整]。",
  project_directory: "."
})
```

**反馈内容细化**：
- 详细描述实际完成的功能和效果
- 说明与预期目标的差异（如有）
- 总结重要的技术发现或经验
- 评估对后续步骤的影响和调整建议

### 2. 问题处理流程

#### 2.1 遇到技术问题 ⚠️ 必须反馈
**操作内容**：
- 详细分析问题原因
- 研究可能的解决方案
- 评估解决方案的影响

**⚠️ 反馈要求**：
```typescript
await mcp_feedback_enhanced({
  summary: "遇到技术问题：[问题描述]。可能原因：[分析]。建议解决方案：[方案1/方案2]。请选择处理方式？",
  project_directory: "."
})
```

**反馈内容细化**：
- 清晰描述问题的具体表现和影响范围
- 提供详细的根因分析
- 给出2-3个可行的解决方案，包括优缺点
- 说明各方案的时间成本和风险评估

#### 2.2 记录问题和解决方案
**操作内容**：
在scratchpad.md中记录：
```markdown
## 问题记录

### [问题标题] - [发现日期]
**问题描述**: [详细描述]
**原因分析**: [根本原因]
**解决方案**: [采用的解决方法]
**经验总结**: [避坑要点]
```

### 3. 经验记录

#### 3.1 重要发现记录
**操作内容**：
在scratchpad.md中实时记录：
```markdown
#### 重要发现
- [时间] [技术发现或最佳实践]
- [时间] [需要注意的细节或陷阱]
- [时间] [可复用的代码模式或工具]
```

#### 3.2 通用经验提取
**操作内容**：
- 识别可复用的经验
- 在experience.md中分类记录
- 避免重复造轮子

### 4. 进度管理

#### 4.1 定期进度检查
**检查频率**：
- 简单任务：每完成1-2个步骤
- 复杂任务：每2-4小时
- 遇到阻塞：立即检查

**检查内容**：
- 当前进度vs计划进度
- 遇到的问题和阻塞
- 后续计划是否需要调整

#### 4.2 计划调整 ⚠️ 必须反馈
**触发条件**：
- 发现新的技术问题
- 需求发生变化
- 时间估算偏差较大
- 发现更好的解决方案

**⚠️ 反馈要求**：
```typescript
await mcp_feedback_enhanced({
  summary: "计划需要调整。原因：[调整原因]。建议将[原计划]调整为[新计划]。预计影响：[时间/范围变化]。",
  project_directory: "."
})
```

**反馈内容细化**：
- 详细说明需要调整的具体原因
- 对比原计划和新计划的差异
- 量化评估时间、资源、范围的变化
- 说明调整后的风险和收益

## ✅ 阶段完成标准

### 单个步骤完成
- [ ] 功能按预期实现
- [ ] 测试验证通过
- [ ] 代码质量符合标准
- [ ] 问题和发现已记录
- [ ] 步骤状态已更新

### 重要里程碑完成
- [ ] 用户已确认阶段成果
- [ ] 重要经验已记录
- [ ] 后续计划已确认
- [ ] 必要的文档已更新

## 🔄 流程转换

### 继续执行
- 还有未完成步骤 → 继续执行循环
- 遇到阻塞问题 → 进入问题处理流程
- 需要调整计划 → 更新计划并获得确认

### 转向归档
- 所有主要步骤完成 → [工作流程3：任务归档](./3-task-archiving.md)
- 任务暂停/取消 → 整理现有成果后归档
- workspace内容过多 → 阶段性归档，重新初始化

## 🚨 异常处理

### 长时间无进展
**处理步骤**：
1. 分析阻塞原因
2. 寻求外部帮助或改变方法
3. 考虑降低任务范围
4. 与用户讨论替代方案

### 频繁变更需求
**处理步骤**：
1. 暂停当前工作
2. 重新进行需求分析
3. 评估已完成工作的价值
4. 决定是否需要重新初始化

### 技术方案证明不可行
**处理步骤**：
1. 立即停止当前实施
2. 详细分析失败原因
3. 研究替代技术方案
4. 与用户讨论后续计划

---

**关键成功要素**: 持续沟通 + 及时调整 + 详细记录 