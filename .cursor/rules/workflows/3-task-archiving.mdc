---
description: 任务完成时的归档总结
alwaysApply: false
---
# 工作流程3：任务归档

整理工作成果，创建归档文档，重置workspace环境。

## 🎯 流程目标

- 完整记录任务成果和经验
- 将重要内容永久保存到archives
- 转移未完成任务到合适位置
- 重置workspace为干净状态

## 📋 前置条件

### 触发条件（满足任一即可）
- 任务已基本完成，需要整理成果
- workspace内容过多（>200行），需要清理
- 发现重要经验，需要永久保存
- 准备开始新的重大任务

### 输入要求
- workspace包含有意义的任务内容（非模板状态）
- 明确知道哪些内容需要保留，哪些可以丢弃

## 🔄 执行步骤

### 1. 归档决策评估 ⚠️ 必须反馈

**评估4个关键问题**：
1. 功能完成了吗？
2. 有重要经验吗？
3. workspace太乱了吗？
4. 有未完成任务吗？

**⚠️ 反馈要求**：
```typescript
await mcp_feedback_enhanced({
  summary: "归档决策评估：[4个问题的答案]。建议[归档/不归档]，理由：[具体原因]。",
  project_directory: "."
})
```

**反馈内容细化**：
- 逐一回答4个评估问题并给出依据
- 说明当前workspace的内容量和重要性
- 评估归档的必要性和紧迫性
- 给出明确的建议和理由

### 2. 内容分析与分类

#### 2.1 分析workspace内容
**操作内容**：
- 分析scratchpad.md中的已完成任务、进行中任务、重要发现
- 分析todo.md中的未完成任务
- 分析experience.md中的经验记录

#### 2.2 内容分类
**分类标准**：
- **核心成果** - 主要功能实现、架构改进
- **重要经验** - 可复用的技术经验、避坑指南
- **未完成任务** - 需要转移到developer/todo.md
- **通用经验** - 需要提取到developer/general-experience.md
- **临时内容** - 可以丢弃的临时记录

### 3. 创建归档文档 ⚠️ 重要决策需反馈

#### 3.1 确定归档编号和名称
**操作内容**：
- 查看docs/archives/README.md确定下一个编号
- 为功能起一个清晰的名称
- 创建归档目录：`docs/archives/[编号]-[功能名称]/`

**⚠️ 反馈要求**（如果功能重要）：
```typescript
await mcp_feedback_enhanced({
  summary: "准备创建归档[编号]-[功能名称]，主要内容：[核心成果]。归档结构：[文档列表]。",
  project_directory: "."
})
```

**反馈内容细化**：
- 说明归档的编号和命名逻辑
- 详细列出将要记录的核心成果
- 展示计划的文档结构和内容分布
- 说明归档的价值和后续参考用途

#### 3.2 创建核心文档
**必需文档**：

**README.md** - 项目概述
```markdown
# [功能名称]

## 📋 项目概述
- 项目编号、名称、时间、状态

## 🎯 项目目标
- 主要目标、技术目标

## ✅ 完成情况
- 核心功能完成情况
- 技术实现完成情况

## 🎉 主要成果
- 架构改进、稳定性提升、开发体验优化

## 🚀 后续工作
- 已识别的待办事项、建议的改进方向
```

**implementation.md** - 技术实现（如果有代码修改）
```markdown
# 技术实现详解

## 🔧 架构设计
## 🐛 问题诊断与解决  
## 📝 实施步骤
## 🔍 调试过程
## 🧪 测试验证
```

**experience.md** - 经验总结（如果有重要经验）
```markdown
# 开发经验总结

## 🎯 核心经验
## 🛠️ 技术实现经验
## 🚫 避坑指南
## 🔄 架构设计经验
```

### 4. 更新项目索引和交叉引用

#### 4.1 更新archives索引
**操作内容**：
- 在docs/archives/README.md中添加新条目
- 更新下一个归档编号
- 按状态分类（已完成/进行中/计划中）

#### 4.2 任务转移 ⚠️ 重要任务需确认
**操作内容**：
- 将未完成的重要任务转移到docs/developer/todo.md
- 按优先级分类（高/中/低优先级）
- 删除已无价值的临时任务

**⚠️ 反馈要求**（如果有重要未完成任务）：
```typescript
await mcp_feedback_enhanced({
  summary: "发现未完成的重要任务：[任务列表]。建议转移到[目标位置]，优先级设为[高/中/低]。",
  project_directory: "."
})
```

**反馈内容细化**：
- 详细列出所有未完成的任务及其当前状态
- 说明每个任务的重要性和紧迫性评估
- 建议具体的转移位置和优先级分配
- 评估任务的完成时间和资源需求

#### 4.3 经验提取
**操作内容**：
- 将通用经验提取到docs/developer/general-experience.md
- 在general-experience.md中添加指向新归档的链接
- 避免经验重复或分散

### 5. Workspace重置 ⚠️ 必须确认

**⚠️ 重置前确认**：
```typescript
await mcp_feedback_enhanced({
  summary: "归档已完成。准备重置workspace，将删除所有当前内容并恢复模板状态。请确认可以继续？",
  project_directory: "."
})
```

**重置操作**：
```batch
del docs\workspace\*.md
copy docs\workspace-template\*.md docs\workspace\
ren docs\workspace\scratchpad-template.md scratchpad.md
ren docs\workspace\todo-template.md todo.md  
ren docs\workspace\experience-template.md experience.md
```

**重置验证**：
- [ ] workspace只有3个文件
- [ ] 所有文件都是模板格式（包含占位符）
- [ ] 没有具体的任务内容

## ✅ 完成标准

### 归档质量检查
- [ ] archives目录包含完整的项目文档
- [ ] README.md清晰描述了项目概述和成果
- [ ] 技术实现和经验文档内容充实
- [ ] archives/README.md已更新索引

### 任务转移检查
- [ ] 重要未完成任务已转移到合适位置
- [ ] 通用经验已提取到general-experience.md
- [ ] 交叉引用链接正确有效

### Workspace状态检查
- [ ] workspace只包含3个模板文件
- [ ] 所有内容都是占位符格式
- [ ] 没有遗留的具体任务信息

## 🔄 后续流程

**成功完成后**：
- workspace干净，可以开始新任务
- 使用[工作流程1：任务初始化](./1-task-initialization.md)开始新工作

**需要调整时**：
- 发现重要内容遗漏 → 补充到归档文档
- 任务转移有误 → 重新整理任务分布
- workspace重置失败 → 手动检查和修复

## 🚨 异常处理

### 归档内容不够充实
**处理步骤**：
1. 回顾原始任务目标
2. 补充缺失的实现细节
3. 添加更多上下文信息
4. 确保后续开发者能理解

### 未完成任务太多
**处理步骤**：
1. 重新评估任务优先级
2. 考虑任务拆分或合并
3. 与用户讨论哪些可以推迟
4. 避免todo.md过于膨胀

### Workspace重置失败
**处理步骤**：
1. 手动检查文件权限
2. 确认模板文件完整性
3. 逐步执行重置命令
4. 验证最终状态

---

**关键成功要素**: 完整记录 + 合理分类 + 彻底清理

## 📊 归档效果评估

好的归档应该能够：
- 让6个月后的开发者快速理解项目
- 避免重复犯同样的错误
- 提供可复用的经验和代码模式
- 为类似项目提供参考架构 # 工作流程3：任务归档

整理工作成果，创建归档文档，重置workspace环境。

## 🎯 流程目标

- 完整记录任务成果和经验
- 将重要内容永久保存到archives
- 转移未完成任务到合适位置
- 重置workspace为干净状态

## 📋 前置条件

### 触发条件（满足任一即可）
- 任务已基本完成，需要整理成果
- workspace内容过多（>200行），需要清理
- 发现重要经验，需要永久保存
- 准备开始新的重大任务

### 输入要求
- workspace包含有意义的任务内容（非模板状态）
- 明确知道哪些内容需要保留，哪些可以丢弃

## 🔄 执行步骤

### 1. 归档决策评估 ⚠️ 必须反馈

**评估4个关键问题**：
1. 功能完成了吗？
2. 有重要经验吗？
3. workspace太乱了吗？
4. 有未完成任务吗？

**⚠️ 反馈要求**：
```typescript
await mcp_feedback_enhanced({
  summary: "归档决策评估：[4个问题的答案]。建议[归档/不归档]，理由：[具体原因]。",
  project_directory: "."
})
```

**反馈内容细化**：
- 逐一回答4个评估问题并给出依据
- 说明当前workspace的内容量和重要性
- 评估归档的必要性和紧迫性
- 给出明确的建议和理由

### 2. 内容分析与分类

#### 2.1 分析workspace内容
**操作内容**：
- 分析scratchpad.md中的已完成任务、进行中任务、重要发现
- 分析todo.md中的未完成任务
- 分析experience.md中的经验记录

#### 2.2 内容分类
**分类标准**：
- **核心成果** - 主要功能实现、架构改进
- **重要经验** - 可复用的技术经验、避坑指南
- **未完成任务** - 需要转移到developer/todo.md
- **通用经验** - 需要提取到developer/general-experience.md
- **临时内容** - 可以丢弃的临时记录

### 3. 创建归档文档 ⚠️ 重要决策需反馈

#### 3.1 确定归档编号和名称
**操作内容**：
- 查看docs/archives/README.md确定下一个编号
- 为功能起一个清晰的名称
- 创建归档目录：`docs/archives/[编号]-[功能名称]/`

**⚠️ 反馈要求**（如果功能重要）：
```typescript
await mcp_feedback_enhanced({
  summary: "准备创建归档[编号]-[功能名称]，主要内容：[核心成果]。归档结构：[文档列表]。",
  project_directory: "."
})
```

**反馈内容细化**：
- 说明归档的编号和命名逻辑
- 详细列出将要记录的核心成果
- 展示计划的文档结构和内容分布
- 说明归档的价值和后续参考用途

#### 3.2 创建核心文档
**必需文档**：

**README.md** - 项目概述
```markdown
# [功能名称]

## 📋 项目概述
- 项目编号、名称、时间、状态

## 🎯 项目目标
- 主要目标、技术目标

## ✅ 完成情况
- 核心功能完成情况
- 技术实现完成情况

## 🎉 主要成果
- 架构改进、稳定性提升、开发体验优化

## 🚀 后续工作
- 已识别的待办事项、建议的改进方向
```

**implementation.md** - 技术实现（如果有代码修改）
```markdown
# 技术实现详解

## 🔧 架构设计
## 🐛 问题诊断与解决  
## 📝 实施步骤
## 🔍 调试过程
## 🧪 测试验证
```

**experience.md** - 经验总结（如果有重要经验）
```markdown
# 开发经验总结

## 🎯 核心经验
## 🛠️ 技术实现经验
## 🚫 避坑指南
## 🔄 架构设计经验
```

### 4. 更新项目索引和交叉引用

#### 4.1 更新archives索引
**操作内容**：
- 在docs/archives/README.md中添加新条目
- 更新下一个归档编号
- 按状态分类（已完成/进行中/计划中）

#### 4.2 任务转移 ⚠️ 重要任务需确认
**操作内容**：
- 将未完成的重要任务转移到docs/developer/todo.md
- 按优先级分类（高/中/低优先级）
- 删除已无价值的临时任务

**⚠️ 反馈要求**（如果有重要未完成任务）：
```typescript
await mcp_feedback_enhanced({
  summary: "发现未完成的重要任务：[任务列表]。建议转移到[目标位置]，优先级设为[高/中/低]。",
  project_directory: "."
})
```

**反馈内容细化**：
- 详细列出所有未完成的任务及其当前状态
- 说明每个任务的重要性和紧迫性评估
- 建议具体的转移位置和优先级分配
- 评估任务的完成时间和资源需求

#### 4.3 经验提取
**操作内容**：
- 将通用经验提取到docs/developer/general-experience.md
- 在general-experience.md中添加指向新归档的链接
- 避免经验重复或分散

### 5. Workspace重置 ⚠️ 必须确认

**⚠️ 重置前确认**：
```typescript
await mcp_feedback_enhanced({
  summary: "归档已完成。准备重置workspace，将删除所有当前内容并恢复模板状态。请确认可以继续？",
  project_directory: "."
})
```

**重置操作**：
```batch
del docs\workspace\*.md
copy docs\workspace-template\*.md docs\workspace\
ren docs\workspace\scratchpad-template.md scratchpad.md
ren docs\workspace\todo-template.md todo.md  
ren docs\workspace\experience-template.md experience.md
```

**重置验证**：
- [ ] workspace只有3个文件
- [ ] 所有文件都是模板格式（包含占位符）
- [ ] 没有具体的任务内容

## ✅ 完成标准

### 归档质量检查
- [ ] archives目录包含完整的项目文档
- [ ] README.md清晰描述了项目概述和成果
- [ ] 技术实现和经验文档内容充实
- [ ] archives/README.md已更新索引

### 任务转移检查
- [ ] 重要未完成任务已转移到合适位置
- [ ] 通用经验已提取到general-experience.md
- [ ] 交叉引用链接正确有效

### Workspace状态检查
- [ ] workspace只包含3个模板文件
- [ ] 所有内容都是占位符格式
- [ ] 没有遗留的具体任务信息

## 🔄 后续流程

**成功完成后**：
- workspace干净，可以开始新任务
- 使用[工作流程1：任务初始化](./1-task-initialization.md)开始新工作

**需要调整时**：
- 发现重要内容遗漏 → 补充到归档文档
- 任务转移有误 → 重新整理任务分布
- workspace重置失败 → 手动检查和修复

## 🚨 异常处理

### 归档内容不够充实
**处理步骤**：
1. 回顾原始任务目标
2. 补充缺失的实现细节
3. 添加更多上下文信息
4. 确保后续开发者能理解

### 未完成任务太多
**处理步骤**：
1. 重新评估任务优先级
2. 考虑任务拆分或合并
3. 与用户讨论哪些可以推迟
4. 避免todo.md过于膨胀

### Workspace重置失败
**处理步骤**：
1. 手动检查文件权限
2. 确认模板文件完整性
3. 逐步执行重置命令
4. 验证最终状态

---

**关键成功要素**: 完整记录 + 合理分类 + 彻底清理

## 📊 归档效果评估

好的归档应该能够：
- 让6个月后的开发者快速理解项目
- 避免重复犯同样的错误
- 提供可复用的经验和代码模式
- 为类似项目提供参考架构 