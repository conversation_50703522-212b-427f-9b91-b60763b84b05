name: Docker Build and Push

on:
  push:
    branches: [main, master]
    paths-ignore:
      - "**.md"
      - "docs/**"

env:
  # Docker Hub 配置
  DOCKERHUB_REGISTRY: docker.io
  DOCKERHUB_IMAGE_NAME: linshen/prompt-optimizer

  # 阿里云容器镜像服务配置
  ALIYUN_REGISTRY: registry.cn-guangzhou.aliyuncs.com
  ALIYUN_IMAGE_NAME: prompt-optimizer/prompt-optimizer

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10.5.2
          run_install: false

      - name: 设置 Node.js 环境
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "pnpm"

      - name: 安装依赖
        run: pnpm install

      - name: 运行构建
        run: pnpm build

      - name: 运行测试
        run: pnpm test

      - name: 获取package.json版本号
        id: version
        run: |
          VERSION=$(grep -m1 '"version":' package.json | cut -d'"' -f4)
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Version from package.json: $VERSION"

      - name: 登录到Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: 登录到阿里云容器镜像服务
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_USERNAME }}
          password: ${{ secrets.ALIYUN_PASSWORD }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 构建并推送Docker镜像
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          tags: |
            ${{ env.DOCKERHUB_REGISTRY }}/${{ env.DOCKERHUB_IMAGE_NAME }}:${{ steps.version.outputs.version }}
            ${{ env.DOCKERHUB_REGISTRY }}/${{ env.DOCKERHUB_IMAGE_NAME }}:latest
            ${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_IMAGE_NAME }}:${{ steps.version.outputs.version }}
            ${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_IMAGE_NAME }}:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max
