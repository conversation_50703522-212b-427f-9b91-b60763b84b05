# 环境变量配置示例文件
# 将此文件复制为 .env.local 并填入你的实际配置

# ===== LLM API 密钥配置 =====
# 配置您要使用的 LLM 服务的 API 密钥
# 不同模块会根据需要使用这些配置

# OpenAI API 配置
# VITE_OPENAI_API_KEY=sk-your-openai-api-key-here

# Google Gemini API 配置
# VITE_GEMINI_API_KEY=your-gemini-api-key-here

# DeepSeek API 配置
# VITE_DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here

# 智谱 AI API 配置
# VITE_ZHIPU_API_KEY=your-zhipu-api-key-here

# SiliconFlow API 配置
# VITE_SILICONFLOW_API_KEY=sk-your-siliconflow-api-key-here

# 自定义 API 配置（如 Ollama 本地服务）
# VITE_CUSTOM_API_KEY=your-custom-api-key
# VITE_CUSTOM_API_BASE_URL=http://localhost:11434/v1
# VITE_CUSTOM_API_MODEL=qwen2.5:0.5b

# ===== 多自定义模型配置（新功能）=====
# 支持配置无限数量的自定义模型，使用后缀区分不同模型
# 格式：VITE_CUSTOM_API_*_<suffix>
# 后缀规则：只能包含字母（a-z A-Z）、数字（0-9）、下划线（_）、连字符（-）
# 不支持：点号（.）、空格、特殊符号等

# Ollama Qwen 模型示例
# VITE_CUSTOM_API_KEY_qwen3=ollama-qwen3-key
# VITE_CUSTOM_API_BASE_URL_qwen3=http://localhost:11434/v1
# VITE_CUSTOM_API_MODEL_qwen3=qwen3:8b

# Ollama Qwen2.5 模型示例（注意：版本号用下划线分隔）
# VITE_CUSTOM_API_KEY_qwen2_5=ollama-qwen25-key
# VITE_CUSTOM_API_BASE_URL_qwen2_5=http://localhost:11434/v1
# VITE_CUSTOM_API_MODEL_qwen2_5=qwen2.5:14b

# 本地 Claude 兼容服务示例
# VITE_CUSTOM_API_KEY_claude_local=claude-local-key
# VITE_CUSTOM_API_BASE_URL_claude_local=http://localhost:8080/v1
# VITE_CUSTOM_API_MODEL_claude_local=claude-3-sonnet

# 其他自建 API 服务示例
# VITE_CUSTOM_API_KEY_my_llm=my-llm-api-key
# VITE_CUSTOM_API_BASE_URL_my_llm=https://my-api.example.com/v1
# VITE_CUSTOM_API_MODEL_my_llm=my-custom-model

# 注意：
# - 以下三项均为必填：API_KEY、BASE_URL、MODEL（与核心实现严格一致）
# - 生成的模型在UI中显示为格式化的名称（如 qwen3 → Qwen3）

# ===== MCP 服务器配置 =====
# 以下配置仅在使用 MCP 服务器时需要

# 首选模型提供商（当配置了多个 API 密钥时）
# 可选值：openai, gemini, deepseek, siliconflow, zhipu, custom, custom_<suffix>
# 注意：必须与上面配置的 API 密钥对应
# 示例：如果配置了 VITE_CUSTOM_API_KEY_qwen3，可以使用 custom_qwen3
# MCP_DEFAULT_MODEL_PROVIDER=openai

# HTTP 服务器端口（默认 3000）
# MCP_HTTP_PORT=3000

# 日志级别（默认 debug）
# 可选值：debug, info, warn, error
# MCP_LOG_LEVEL=debug

# 默认语言（默认 zh）
# 可选值：zh, en
# MCP_DEFAULT_LANGUAGE=zh

# ===== Docker 部署访问控制配置 =====
# 以下配置仅在 Docker 部署时需要，用于设置 Web 界面的访问控制

# 访问用户名（可选，默认为 admin）
# ACCESS_USERNAME=admin

# 访问密码（可选，不设置则无密码保护）
# ACCESS_PASSWORD=your_password

# ===== 开发环境更新测试配置 =====
# 取消注释以下配置来启用开发环境的更新测试功能

# 方式1：使用 GITHUB_REPOSITORY 环境变量（推荐）
# GITHUB_REPOSITORY=your-username/your-repo-name

# 方式2：分别设置仓库所有者和名称
# DEV_REPO_OWNER=your-username
# DEV_REPO_NAME=your-repo-name

# ===== 使用说明 =====
#
# LLM API 密钥：
# 1. 使用 VITE_ 前缀的环境变量，支持所有模块（Web、Desktop、MCP 服务器等）
# 2. 根据使用的功能配置相应的 API 密钥
# 3. 自定义 API 支持本地服务（如 Ollama）和其他兼容 OpenAI 格式的服务
# 4. 多自定义模型：使用 VITE_CUSTOM_API_*_<suffix> 格式配置多个自定义模型
#    - 支持无限数量的自定义模型
#    - 每个模型在UI中显示为独立选项
#    - 后缀名会自动格式化为友好的显示名称
#
# MCP 服务器（仅在使用 MCP 功能时需要）：
# 1. 需要至少配置一个 API 密钥才能启动
# 2. 在项目根目录执行 pnpm mcp:dev 启动开发服务器
# 3. 如果配置多个 API 密钥，可通过 MCP_DEFAULT_MODEL_PROVIDER 指定首选
# 4. MCP_DEFAULT_MODEL_PROVIDER 支持的值：
#    - openai: 使用 OpenAI GPT 模型
#    - gemini: 使用 Google Gemini 模型
#    - deepseek: 使用 DeepSeek 模型
#    - siliconflow: 使用 SiliconFlow 模型
#    - zhipu: 使用智谱 AI 模型
#    - custom: 使用自定义 API（如 Ollama 等）
#    - custom_<suffix>: 使用特定的多自定义模型（如 custom_qwen3）
# 5. HTTP 模式默认端口 3000，可通过 MCP_HTTP_PORT 修改
# 6. 默认启用 debug 日志，可通过 MCP_LOG_LEVEL 调整
#
# Docker 部署访问控制：
# 1. ACCESS_USERNAME 和 ACCESS_PASSWORD 仅在 Docker 部署时使用
# 2. 不设置密码则无访问限制
# 3. 设置密码后访问 Web 界面需要输入用户名和密码
#
# 更新测试（仅在开发环境需要）：
# 1. 只支持公开仓库，不支持私有仓库
# 2. 如果不设置仓库配置，将使用默认的公开仓库 (linshenkx/prompt-optimizer)
# 3. 开发环境现在默认支持更新测试，无需额外配置文件
# 4. 更新日志将保存在用户数据目录的 logs/auto-updater.log 文件中