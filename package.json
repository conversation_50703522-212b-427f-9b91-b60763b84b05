{"name": "prompt-optimizer", "version": "1.4.4", "private": true, "packageManager": "pnpm@10.6.1", "engines": {"node": "^18.0.0 || ^20.0.0 || ^22.0.0", "npm": "请使用pnpm代替npm", "yarn": "请使用pnpm代替yarn"}, "scripts": {"build": "npm-run-all build:core build:ui build:parallel", "build:core": "pnpm -F @prompt-optimizer/core build", "build:ui": "pnpm -F @prompt-optimizer/ui build", "build:parallel": "npm-run-all --parallel build:web build:ext", "build:web": "pnpm -F @prompt-optimizer/web build", "build:ext": "pnpm -F @prompt-optimizer/extension build", "build:desktop-only": "pnpm -F @prompt-optimizer/desktop build", "build:desktop": "npm-run-all build:core build:ui build:web build:desktop-only", "dev": "npm-run-all clean:dist build:core build:ui dev:parallel", "dev:fresh": "npm-run-all clean pnpm-install dev", "dev:parallel": "concurrently -k -p \"[{name}]\" -n \"UI,WEB\" \"pnpm -F @prompt-optimizer/ui build --watch\" \"pnpm -F @prompt-optimizer/web dev\"", "dev:ext": "pnpm -F @prompt-optimizer/extension dev", "dev:desktop": "npm-run-all clean:dist build:core build:ui dev:desktop:parallel", "dev:desktop:fresh": "npm-run-all clean pnpm-install dev:desktop", "dev:desktop:parallel": "concurrently -k -p \"[{name}]\" -n \"WEB,DESKTOP\" \"pnpm -F @prompt-optimizer/web dev\" \"pnpm -F @prompt-optimizer/desktop dev\"", "test": "pnpm -r test --run --passWithNoTests", "clean": "npm-run-all clean:dist clean:vite", "clean:dist": "rimraf packages/core/dist packages/ui/dist packages/web/dist packages/extension/dist packages/desktop/dist packages/desktop/web-dist", "clean:vite": "rimraf packages/core/node_modules/.vite packages/ui/node_modules/.vite packages/web/node_modules/.vite packages/extension/node_modules/.vite", "pnpm-install": "pnpm install", "version:sync": "node scripts/sync-versions.js", "version": "pnpm run version:sync && git add -A", "version:prepare": "pnpm version --no-git-tag-version", "version:tag": "git tag v$(node -p \"require('./package.json').version\")", "version:publish": "git push origin v$(node -p \"require('./package.json').version\")", "mcp:build": "pnpm --filter @prompt-optimizer/mcp-server build", "mcp:dev": "pnpm --filter @prompt-optimizer/mcp-server dev", "mcp:start": "pnpm --filter @prompt-optimizer/mcp-server start", "mcp:test": "pnpm --filter @prompt-optimizer/mcp-server test"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^6.0.3", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^37.1.0", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "lodash-unified": "^1.0.3", "npm-run-all": "^4.1.5", "rimraf": "^4.4.1", "typescript": "^5.8.2"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@floating-ui/core": "^1.6.9", "@floating-ui/dom": "^1.6.13", "@floating-ui/utils": "^0.2.9", "@popperjs/core": "^2.11.8", "@vue/reactivity": "^3.5.13", "@vue/runtime-core": "^3.5.13", "@vue/runtime-dom": "^3.5.13", "@vue/shared": "^3.5.13", "@vueuse/core": "^12.7.0", "@vueuse/shared": "^12.7.0", "async-validator": "^4.2.5", "dayjs": "^1.11.13", "electron-to-chromium": "^1.5.177", "lodash-es": "^4.17.21", "memoize-one": "^6.0.0", "normalize-wheel-es": "^1.2.0", "vue-i18n": "^10.0.6"}, "keywords": [], "author": "", "license": "ISC", "pnpm": {"onlyBuiltDependencies": ["electron"], "overrides": {"builder-util-runtime": "9.2.10"}}}