{"root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "env": {"node": true, "es2022": true}, "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "prefer-const": "error", "no-var": "error", "no-console": "off", "no-duplicate-imports": "error", "no-undef": "off"}, "ignorePatterns": ["dist/", "node_modules/", "*.js", "*.cjs", "*.mjs"]}