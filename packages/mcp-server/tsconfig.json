{"compilerOptions": {"outDir": "./dist", "rootDir": "./src", "module": "ESNext", "target": "ES2022", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "lib": ["ES2022", "DOM"]}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "tests", "**/*.test.ts"]}