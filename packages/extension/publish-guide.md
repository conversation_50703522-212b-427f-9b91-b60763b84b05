# Chrome扩展上架指南

本文档提供了将提示词优化器发布到Chrome网上应用商店的详细步骤。

## 准备工作

### 1. 开发者账号注册

在上传扩展前，需要先注册Chrome网上应用商店开发者账号：

1. 访问 [Chrome网上应用商店开发者控制台](https://chrome.google.com/webstore/devconsole)
2. 使用Google账号登录
3. 支付一次性的$5.00 USD开发者注册费
4. 完成开发者资料设置

### 2. 准备插件包

1. 确保已完成构建：

   ```bash
   pnpm build:ext
   ```

2. 打包扩展：
   - 找到构建后的扩展目录（通常在`packages/extension/dist`）
   - 将整个目录打包为ZIP文件
   - 确保ZIP文件结构根目录直接包含manifest.json

### 3. 准备上架材料

根据`chrome.md`文件中列出的清单，确保准备好所有所需材料：

- 所有尺寸的图标
- 至少1张截图（1280x800或640x400像素）
- 宣传图片（可选，1400x560像素）
- 详细描述文本
- 隐私政策页面

## 上传流程

1. 访问 [Chrome网上应用商店开发者控制台](https://chrome.google.com/webstore/devconsole)
2. 点击"添加新项目"按钮
3. 上传ZIP格式的扩展包
4. 填写商品详情页信息：
   - 语言：选择中文和英文
   - 商品详情：复制`chrome.md`中的详细描述
   - 类别：选择"生产力工具"和"写作工具"
   - 上传图标、截图和宣传图片
   - 填写隐私政策链接（可使用GitHub Pages或Vercel托管的隐私政策页面）
5. 提交审核前，检查所有内容是否完整

## 审核流程

Chrome网上应用商店的审核通常需要几天到两周时间，请耐心等待。

### 常见审核问题及解决方案

1. **权限问题**：
   - 确保manifest.json中只请求必要的权限
   - 在描述中明确说明每个权限的用途

2. **隐私政策问题**：
   - 确保隐私政策完整详尽
   - 明确说明如何处理用户数据

3. **功能描述问题**：
   - 确保描述准确，不夸大功能
   - 所有截图需真实反映插件功能

4. **安全问题**：
   - 确保不存在恶意代码
   - 避免使用不安全的API

## 发布后维护

### 版本更新

1. 增加manifest.json中的版本号
2. 重新构建插件
3. 打包新版本
4. 在开发者控制台上传新版本
5. 填写更新说明
6. 提交审核

### 用户反馈处理

1. 定期检查用户评价和反馈
2. 及时回复用户问题
3. 根据反馈改进插件功能
4. 更新常见问题解答

## 宣传策略

1. **社交媒体宣传**：
   - 在技术社区分享（如掘金、知乎、V2EX等）
   - 制作简短演示视频
   - 编写使用教程和案例

2. **SEO优化**：
   - 优化Chrome商店描述关键词
   - 创建专门的落地页
   - 编写相关博客文章

3. **用户激励**：
   - 鼓励满意用户评价
   - 提供反馈奖励机制
   - 建立用户社区

## 财务管理

1. 设置Google商家账户（如适用）
2. 设置税务信息
3. 设置付款方式
4. 了解应用内购买政策（如适用）

## 资源链接

- [Chrome开发者文档](https://developer.chrome.com/docs/webstore/)
- [Chrome商店政策](https://developer.chrome.com/docs/webstore/program-policies/)
- [Chrome扩展最佳实践](https://developer.chrome.com/docs/extensions/mv3/best_practices/)
- [Google商家支持](https://support.google.com/chrome_webstore/)
