{"name": "@prompt-optimizer/extension", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@prompt-optimizer/ui": "workspace:*", "element-plus": "^2.9.3", "uuid": "^11.0.5", "vue": "^3.5.13"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tsconfig/node18": "^18.2.4", "@types/node": "^22.13.4", "@types/uuid": "^10.0.0", "@vitejs/plugin-basic-ssl": "^1.2.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.0.0", "vite": "^6.0.7", "vitest": "^3.0.2", "dotenv": "^16.4.7", "js-yaml": "^4.1.0"}}