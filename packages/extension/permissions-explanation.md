# 提示词优化器 - 权限说明

本文档详细解释了提示词优化器Chrome扩展请求的各项权限及其用途，帮助您了解我们为何需要这些权限以及它们如何用于提供服务。

## 单一用途说明 (Single Purpose Description)

**提示词优化器的主要目的是：优化用户的AI提示词，以获得更精准、高质量的AI回复。**

本扩展请求的所有权限都直接服务于这一单一核心目的：

- **存储权限**用于保存用户的API密钥和设置，使用户能够连接到AI服务
- **API域名访问权限**用于直接从用户浏览器发送提示词优化请求到相应的AI服务提供商
- **标签页权限**用于在标签页中打开设置界面，提供更好的用户体验

这些权限对于实现扩展的主要功能至关重要，没有这些权限，扩展将无法执行其核心功能。同时，我们严格遵循最小权限原则，仅请求完成核心任务所必需的权限，不收集任何与此核心功能无关的数据。

## 请求的权限

### 存储权限 (`storage`)

**用途：**

- 在本地安全存储API密钥
- 保存您的扩展设置和偏好
- 存储提示词优化历史记录（如果启用了此功能）

**重要说明：**

- 所有数据仅存储在您的本地设备上
- 不会上传到任何服务器
- 您可以随时通过扩展设置清除这些数据

### 标签页权限 (`tabs`)

**用途：**

- 允许扩展在标签页中展示所有核心功能，包括提示词优化、模型管理以及其他用户交互界面
- 提供更宽敞、更灵活的用户体验，让所有功能都在独立标签页中运行

**重要说明：**

- 扩展在用户主动操作时会在标签页中打开并展示所有功能界面
- 不会在后台监控您的标签页
- 不会记录您的浏览历史或收集标签页数据
- 仅用于提供更好的用户界面体验，所有核心功能均在标签页中完成

## 主机权限 (`host_permissions`)

### 1. OpenAI API (`https://api.openai.com/*`)

**用途：**

- 允许扩展直接从您的浏览器向OpenAI的API发送请求
- 用于使用OpenAI模型（如GPT-3.5、GPT-4）优化提示词

### 2. Google Gemini API (`https://generativelanguage.googleapis.com/*`)

**用途：**

- 允许扩展直接从您的浏览器向Google的Gemini API发送请求
- 用于使用Google Gemini模型优化提示词

### 3. DeepSeek API (`https://api.deepseek.com/*`)

**用途：**

- 允许扩展直接从您的浏览器向DeepSeek的API发送请求
- 用于使用DeepSeek模型优化提示词

### 4. SiliconFlow API (`https://api.siliconflow.cn/*`)

**用途：**

- 允许扩展直接从您的浏览器向SiliconFlow的API发送请求
- 用于使用SiliconFlow模型优化提示词

**重要说明：**

- 所有API请求都直接从您的浏览器发起
- 使用您自己提供的API密钥
- 不经过我们的服务器或任何中间服务器
- 您的提示词内容会发送到相应的AI服务提供商，并受其隐私政策约束

## 关于自定义API的说明

虽然扩展在manifest中只预先声明了OpenAI、Google Gemini和DeepSeek的API域名权限，但您仍然可以使用自定义API。这是因为：

1. **内容安全策略的工作方式**：Chrome扩展的内容安全策略允许扩展在运行时连接到用户明确授权的域名，即使这些域名没有在manifest中预先声明。

2. **如何使用自定义API**：
   - 在扩展的"模型管理"界面中，点击"添加自定义模型"
   - 填写自定义API的相关信息，包括API地址、模型名称和API密钥
   - 保存后，您就可以使用这个自定义API了

3. **首次连接授权**：
   - 当您首次尝试使用自定义API时，Chrome浏览器会显示一个权限请求对话框
   - 您需要明确授权扩展连接到这个自定义域名
   - 授权后，扩展就可以与该自定义API通信了

4. **安全考虑**：
   - 这种方式确保了扩展只能连接到您明确授权的域名
   - 所有连接仍然是从您的浏览器直接发起的，不经过我们的服务器
   - 您可以随时在Chrome的扩展权限设置中撤销这些授权

5. **适用场景**：
   - 使用公司内部的AI服务
   - 连接到其他兼容OpenAI格式的API服务
   - 使用自托管的开源模型API
   - 连接到任何符合OpenAI兼容格式的API端点

这种设计既保证了扩展的灵活性，允许您连接到任何需要的API服务，同时也维护了Chrome扩展的安全模型，确保所有连接都是经过您明确授权的。

## 为什么需要特定API域名的访问权限？

Chrome浏览器的安全机制要求扩展明确声明它需要访问的外部域名。这是一项重要的安全功能，称为"内容安全策略"(Content Security Policy)，它限制扩展只能与预先声明的域名通信，防止恶意扩展向未授权的服务器发送数据。

### 为什么我们需要这些特定域名的权限：

1. **直接API调用的必要条件**：
   - 没有这些权限，浏览器会阻止扩展向这些AI服务提供商发送请求
   - 这些权限是实现"纯客户端应用"架构的关键，使扩展能够直接从您的浏览器调用AI服务，而无需通过我们的服务器

2. **精确的最小权限原则**：
   - 我们只请求访问必要的API域名（如OpenAI、Google Gemini、DeepSeek）
   - 不请求访问其他不相关的域名
   - 每个请求的域名都有明确的用途（提供AI模型服务）

3. **透明的数据流向**：
   - 这些权限使数据流向完全透明 - 从您的浏览器直接到AI服务提供商
   - 没有中间服务器或第三方参与数据传输过程

4. **安全性考虑**：
   - 使用通配符模式（如`https://api.openai.com/*`）允许访问该域名下的所有路径
   - 这是必要的，因为AI服务提供商可能在不同路径提供不同的API端点
   - 所有通信都通过安全的HTTPS连接进行

### 如果没有这些权限会怎样？

如果我们不请求这些特定API域名的访问权限：

1. 扩展将无法直接调用AI服务提供商的API
2. 提示词优化功能将无法工作
3. 我们将不得不采用服务器中转架构，这会：
   - 降低数据隐私性（您的数据需要经过我们的服务器）
   - 增加延迟（多一层数据传输）
   - 引入额外的安全风险

### 用户控制

重要的是，即使授予了这些权限：

- 扩展只会在您主动使用提示词优化功能时才会发送请求
- 您可以随时在扩展设置中更改或删除API密钥，控制是否允许调用这些服务
- 所有API调用都使用您自己提供的API密钥，您完全控制费用和使用情况

## 最小权限原则

我们遵循最小权限原则，只请求应用程序核心功能所必需的权限。这个扩展只需要：

1. `storage` 权限 - 用于本地存储您的设置和API密钥
2. 特定API域名的访问权限 - 用于直接从您的浏览器调用AI服务

## 我们不请求的权限

为了保护您的隐私，我们特意不请求以下权限：

- **浏览历史** (`history`) - 我们不访问您的浏览历史
- **所有网站的访问权限** (`<all_urls>` 作为权限) - 我们不需要访问您浏览的网页内容
- **活动标签页** (`activeTab`) - 我们不需要访问您当前浏览的页面内容
- **上下文菜单** (`contextMenus`) - 我们不添加右键菜单项
- **后台运行权限** (`background` 作为持续运行的后台) - 我们的扩展不会在后台持续运行
- **网络请求拦截** (`webRequest`) - 我们不拦截或修改您的网络请求
- **Cookie访问** (`cookies`) - 我们不读取或修改您的Cookie

## 纯客户端架构

提示词优化器采用纯客户端架构，这意味着：

1. 所有数据存储和处理都在您的本地浏览器中完成
2. 没有自有服务器收集或处理您的数据
3. API调用直接从您的浏览器发送到AI服务提供商
4. 您的API密钥和设置仅存储在您的设备上

## 权限使用透明度

我们承诺：

- 仅请求提供核心功能所必需的权限
- 明确解释每项权限的用途
- 不滥用任何授予的权限
- 保持代码开源，确保透明度

如果您对我们的权限使用有任何疑问，请通过GitHub Issues联系我们：https://github.com/linshenkx/prompt-optimizer/issues
