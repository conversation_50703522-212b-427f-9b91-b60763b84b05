[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=false
startretries=0
stderr_logfile=/var/log/supervisor/nginx.err.log
stdout_logfile=/var/log/supervisor/nginx.out.log
priority=100

[program:mcp-server]
command=pnpm start --port=3000
directory=/app/mcp-server
autostart=true
autorestart=false
startretries=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
priority=200
environment=NODE_ENV=production,NODE_PATH=/app

[program:node-proxy]
command=node server.js
directory=/app/node-proxy
autostart=true
autorestart=false
startretries=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
priority=150
environment=NODE_ENV=production,PORT=3001,STREAM_TIMEOUT=300000,PROXY_TIMEOUT=120000

[unix_http_server]
file=/var/run/supervisor.sock

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
